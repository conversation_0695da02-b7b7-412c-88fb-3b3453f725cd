'use client';

import { ActionIcon } from '@mantine/core';
import { IconHome } from '@tabler/icons-react';
import { useRouter, usePathname } from 'next/navigation';

export function HomeButton() {
  const router = useRouter();
  const pathname = usePathname();

  // Don't show the home button if we're already on the homepage
  if (pathname === '/') {
    return null;
  }

  const handleHomeClick = () => {
    router.push('/');
  };

  return (
    <ActionIcon
      onClick={handleHomeClick}
      variant="outline"
      size="lg"
      radius="md"
      aria-label="Go to homepage"
    >
      <IconHome size={20} stroke={1.5} />
    </ActionIcon>
  );
}
