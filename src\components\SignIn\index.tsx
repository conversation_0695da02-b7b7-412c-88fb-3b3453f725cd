"use client"

import { FacebookButton } from "src/components/Buttons/FacebookButton"
import { GoogleButton } from "src/components/Buttons/GoogleButton"
import { Title, Stack, Container, Box, Text, Alert, Group, Blockquote } from "@mantine/core"
import { IconCoins, IconGift } from "@tabler/icons-react"
import { APP_VERSION, SIGNUP_POINT, USD_POINT_RATE } from '../../lib/config';

import styles from "./index.module.scss"

import { useSession } from "next-auth/react";

export const SignIn = () => {
const icon = <IconGift />;
  return (
    <Container
      className={styles.signin_container}
      p="3rem"
      h="10vh"
      w="100%"
      display="flex"
      style={{
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Box ta="center" w="100%">
        <Title
          order={1}
          ta="center"
          mb="md"
        >
          Sign in
        </Title>

        {/* Signup Incentive */}
     
         <Blockquote color="blue" cite="Unlock Features" icon={icon} mt="xl">
            <IconCoins size={16} />
            <Text size="sm" fw={500}>
              Get {SIGNUP_POINT} points = ${SIGNUP_POINT * USD_POINT_RATE} instantly when you sign up!
            </Text>
          </Blockquote>
        
    <br/>

        <Stack className={styles.btn_group} align="center">
          <GoogleButton />
          <Text>{APP_VERSION}</Text>
          
        </Stack>
      </Box>
    </Container>
  )
}
