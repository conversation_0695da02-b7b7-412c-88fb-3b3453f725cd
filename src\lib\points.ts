import { getSupabase<PERSON><PERSON>, fetchData, insertData, updateData } from './supabase';
import { SIGNUP_POINT, CREATE_CONTACT_POINT, BOOKMARK_POINT } from './config';

export type TransactionType = 'SIGNUP' | 'CREATE_CONTACT' | 'TRANSFER_SEND' | 'TRANSFER_RECEIVE' | 'TRANSFER_ROLLBACK' | 'ADMIN_LOAD' | 'ADMIN_UNLOAD';

export interface UserPoints {
  email: string;
  points: number;
  created_at: string;
  updated_at: string;
}

export interface TransactionLog {
  id: number;
  email: string;
  transaction_type: TransactionType;
  points_change: number;
  points_before: number;
  points_after: number;
  description: string | null;
  reference_id: string | null;
  from_email: string | null;
  to_email: string | null;
  created_at: string;
}

/**
 * Get user's current point balance
 * Returns null if user doesn't exist, 0 or positive number if user exists
 */
export async function getUserPoints(email: string): Promise<number | null> {
  try {
    const { data, error } = await fetchData<UserPoints>('user_points', {
      select: 'points',
      filter: [{ column: 'email', value: email }],
      single: true
    });

    if (error) {
      // If it's a "not found" error, return null to indicate user doesn't exist
      if (error.message?.includes('No rows returned') || error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching user points:', error);
      return null;
    }

    return data ? (data as UserPoints).points : null;
  } catch (error) {
    console.error('Error in getUserPoints:', error);
    return null;
  }
}

/**
 * Initialize user points (called during signup)
 */
export async function initializeUserPoints(email: string): Promise<boolean> {
  try {
    const { error } = await insertData('user_points', {
      email,
      points: 0
    });

    if (error) {
      console.error('Error initializing user points:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in initializeUserPoints:', error);
    return false;
  }
}

/**
 * Add or subtract points using database function for atomic operation
 */
export async function updateUserPoints(
  email: string,
  pointsChange: number,
  transactionType: TransactionType,
  description?: string,
  referenceId?: string,
  fromEmail?: string,
  toEmail?: string
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase.rpc('update_user_points', {
      user_email: email,
      points_change: pointsChange,
      transaction_type: transactionType,
      description: description || null,
      reference_id: referenceId || null,
      from_email: fromEmail || null,
      to_email: toEmail || null
    });

    if (error) {
      console.error('Error updating user points:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Error in updateUserPoints:', error);
    return false;
  }
}

/**
 * Update user points without creating transaction log (for bookmarks)
 */
export async function updateUserPointsOnly(
  email: string,
  pointsChange: number
): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase.rpc('update_user_points_only', {
      user_email: email,
      points_change: pointsChange
    });

    if (error) {
      console.error('Error updating user points only:', error);
      return false;
    }

    return data === true;
  } catch (error) {
    console.error('Error in updateUserPointsOnly:', error);
    return false;
  }
}

/**
 * Award signup points to new user
 */
export async function awardSignupPoints(email: string): Promise<boolean> {
  return await updateUserPoints(
    email,
    SIGNUP_POINT,
    'SIGNUP',
    `Welcome bonus: ${SIGNUP_POINT} points`
  );
}

/**
 * Deduct points for creating a contact
 */
export async function deductContactCreationPoints(email: string, contactName: string): Promise<boolean> {
  return await updateUserPoints(
    email,
    -CREATE_CONTACT_POINT,
    'CREATE_CONTACT',
    `Contact creation: ${contactName}`,
    contactName
  );
}

/**
 * Award points for bookmark (both to contact owner and bookmarker) - No transaction logging
 */
export async function awardBookmarkPoints(
  bookmarkerEmail: string,
  contactOwnerEmail: string,
  contactName: string
): Promise<{ bookmarkerSuccess: boolean; ownerSuccess: boolean }> {
  const bookmarkerSuccess = await updateUserPointsOnly(
    bookmarkerEmail,
    BOOKMARK_POINT
  );

  const ownerSuccess = await updateUserPointsOnly(
    contactOwnerEmail,
    BOOKMARK_POINT
  );

  return { bookmarkerSuccess, ownerSuccess };
}

/**
 * Deduct points for bookmark removal (both from contact owner and bookmarker) - No transaction logging
 */
export async function deductBookmarkPoints(
  bookmarkerEmail: string,
  contactOwnerEmail: string,
  contactName: string
): Promise<{ bookmarkerSuccess: boolean; ownerSuccess: boolean }> {
  const bookmarkerSuccess = await updateUserPointsOnly(
    bookmarkerEmail,
    -BOOKMARK_POINT
  );

  const ownerSuccess = await updateUserPointsOnly(
    contactOwnerEmail,
    -BOOKMARK_POINT
  );

  return { bookmarkerSuccess, ownerSuccess };
}

/**
 * Check if user has enough points for an operation
 */
export async function hasEnoughPoints(email: string, requiredPoints: number): Promise<boolean> {
  const currentPoints = await getUserPoints(email);
  return currentPoints !== null && currentPoints >= requiredPoints;
}

/**
 * Get user's transaction history
 */
export async function getUserTransactionHistory(
  email: string,
  limit: number = 50
): Promise<TransactionLog[]> {
  try {
    const { data, error } = await fetchData<TransactionLog[]>('transaction_logs', {
      select: '*',
      filter: [{ column: 'email', value: email }],
      order: { column: 'created_at', ascending: false },
      limit
    });

    if (error) {
      console.error('Error fetching transaction history:', error);
      return [];
    }

    return data as TransactionLog[] || [];
  } catch (error) {
    console.error('Error in getUserTransactionHistory:', error);
    return [];
  }
}

/**
 * Get all transaction history for admin (with pagination and filtering)
 */
export async function getAllTransactionHistory(
  page: number = 1,
  limit: number = 50,
  filters?: {
    email?: string;
    transactionType?: string;
    dateFrom?: string;
    dateTo?: string;
    searchQuery?: string;
  }
): Promise<{ data: TransactionLog[]; total: number; totalPages: number }> {
  try {
    const offset = (page - 1) * limit;
    const supabase = getSupabaseClient();

    let query = supabase
      .from('transaction_logs')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (filters?.email) {
      query = query.eq('email', filters.email);
    }

    if (filters?.transactionType) {
      query = query.eq('transaction_type', filters.transactionType);
    }

    if (filters?.dateFrom) {
      query = query.gte('created_at', filters.dateFrom);
    }

    if (filters?.dateTo) {
      query = query.lte('created_at', filters.dateTo);
    }

    if (filters?.searchQuery) {
      query = query.or(`description.ilike.%${filters.searchQuery}%,reference_id.ilike.%${filters.searchQuery}%`);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching all transaction history:', error);
      return { data: [], total: 0, totalPages: 0 };
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      data: data as TransactionLog[] || [],
      total,
      totalPages
    };
  } catch (error) {
    console.error('Error in getAllTransactionHistory:', error);
    return { data: [], total: 0, totalPages: 0 };
  }
}

/**
 * Transfer points between users
 */
export async function transferPoints(
  senderEmail: string,
  recipientOdudeName: string,
  pointsAmount: number,
  description?: string
): Promise<{ success: boolean; message: string }> {
  try {
    // First, get the recipient's email from their ODude name
    const recipientEmail = await getContactOwnerEmail(recipientOdudeName);

    if (!recipientEmail) {
      return {
        success: false,
        message: `ODude name "${recipientOdudeName}" not found or has no associated email.`
      };
    }

    // Prevent self-transfer
    if (senderEmail === recipientEmail) {
      return {
        success: false,
        message: 'You cannot transfer points to yourself.'
      };
    }

    // Check if recipient has a profile (exists in user_points table)
    const recipientPoints = await getUserPoints(recipientEmail);
    if (recipientPoints === null) {
      return {
        success: false,
        message: `Recipient "${recipientOdudeName}" does not have a registered profile account.`
      };
    }

    // Use the database function to transfer points
    const supabase = getSupabaseClient();

    const { data, error } = await supabase.rpc('transfer_points', {
      sender_email: senderEmail,
      recipient_email: recipientEmail,
      points_amount: pointsAmount,
      description: description || `Points transferred to ${recipientOdudeName}`
    });

    if (error) {
      console.error('Error transferring points:', error);
      return {
        success: false,
        message: 'Failed to transfer points. Please try again.'
      };
    }

    if (data === false) {
      return {
        success: false,
        message: 'Transfer failed. You may not have enough points or the recipient account is invalid.'
      };
    }

    return {
      success: true,
      message: `Successfully transferred ${pointsAmount} points to ${recipientOdudeName}.`
    };
  } catch (error) {
    console.error('Error in transferPoints:', error);
    return {
      success: false,
      message: 'An error occurred during the transfer. Please try again.'
    };
  }
}

/**
 * Get contact owner email by contact name
 */
export async function getContactOwnerEmail(contactName: string): Promise<string | null> {
  try {
    const { data, error } = await fetchData('contact', {
      select: 'profile_email',
      filter: [{ column: 'name', value: contactName.toLowerCase() }],
      single: true
    });

    if (error || !data) {
      // Don't log error for non-existent contacts as this is expected behavior
      // Only log if it's an actual database error, not a "not found" case
      if (error && !error.message?.includes('No rows returned')) {
        console.error('Error fetching contact owner:', error);
      }
      return null;
    }

    return (data as any).profile_email;
  } catch (error) {
    console.error('Error in getContactOwnerEmail:', error);
    return null;
  }
}

/**
 * Get user's own contact name by email (for privacy in transaction logs)
 */
export async function getUserOwnContactName(userEmail: string): Promise<string | null> {
  try {
    const { data, error } = await fetchData('contact', {
      select: 'name',
      filter: [{ column: 'profile_email', value: userEmail }],
      single: true
    });

    if (error || !data) {
      return null;
    }

    return (data as any).name;
  } catch (error) {
    console.error('Error in getUserOwnContactName:', error);
    return null;
  }
}
