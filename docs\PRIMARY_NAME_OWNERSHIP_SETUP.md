# Primary Name Ownership Setup Guide

This guide explains how to set up and manage the Primary Name Ownership feature in ODude Names.

## Overview

The Primary Name Ownership feature allows specific users to have admin-like access to a particular namespace (primary name) while maintaining the super admin's full control over the entire system.

## Database Setup

### 1. Add the Primary Name Owners Table

Run the SQL script to add the required table:

```bash
# Using Supabase Dashboard
# 1. Go to your Supabase project dashboard
# 2. Navigate to SQL Editor
# 3. Run the contents of scripts/add-primary-name-owners-table.sql

# Or using psql command line
psql -h your-host -U your-user -d your-database -f scripts/add-primary-name-owners-table.sql
```

### 2. Verify Table Creation

Check that the table was created successfully:

```sql
SELECT * FROM primary_name_owners;
```

## Assigning Primary Name Ownership

### Adding New Owners

To assign a user ownership of a primary name:

```sql
INSERT INTO primary_name_owners (user_email, owner_of) VALUES 
('<EMAIL>', 'shop'),
('<EMAIL>', 'me'),
('<EMAIL>', 'info');
```

### Multiple Ownership

A single user can own multiple primary names:

```sql
INSERT INTO primary_name_owners (user_email, owner_of) VALUES 
('<EMAIL>', 'shop'),
('<EMAIL>', 'store'),
('<EMAIL>', 'business');
```

### Viewing Current Assignments

```sql
SELECT user_email, owner_of, created_at 
FROM primary_name_owners 
ORDER BY created_at DESC;
```

### Removing Ownership

```sql
DELETE FROM primary_name_owners 
WHERE user_email = '<EMAIL>' AND owner_of = 'shop';
```

## Access Levels

### Super Admin
- **Email**: Configured in `src/lib/config.ts` as `ADMIN_EMAIL`
- **Access**: Full system control
- **Features**: All admin features including system info, backups, and all namespaces

### Primary Name Owner
- **Email**: Listed in `primary_name_owners` table
- **Access**: Limited to assigned namespace(s)
- **Features**: 
  - View/manage contacts in owned namespace only
  - View profiles with contacts in owned namespace
  - Namespace-specific statistics
  - No access to system info or backups

### Regular User
- **Access**: Standard user features only
- **Features**: Create contacts, manage profile, use point system

## How It Works

### Namespace Filtering

When a primary name owner logs in:

1. **Contact Access**: Can only see contacts ending with `@{owned_primary_name}`
   - Example: Owner of "shop" sees `hello@shop`, `store@shop`, etc.
   - Cannot see `john@me`, `alice@info`, etc.

2. **Profile Access**: Can only see profiles of users who have created contacts in their namespace

3. **Statistics**: All counts and analytics are filtered to their namespace only

### Dashboard Differences

**Super Admin Dashboard**:
- Full statistics across all namespaces
- System Info tab
- Backup management
- All contacts and profiles

**Owner Dashboard**:
- Statistics for owned namespace(s) only
- No System Info tab
- No backup management
- Filtered contacts and profiles

## Security Considerations

### Authentication Flow

1. User logs in via OAuth (Google, GitHub, Facebook)
2. System checks if user email matches `ADMIN_EMAIL` (super admin)
3. If not super admin, checks `primary_name_owners` table
4. Grants appropriate access level based on findings

### API Protection

All admin API routes now check:
- Authentication status
- Super admin vs owner permissions
- Namespace ownership for data access

### Client-Side Protection

The admin dashboard automatically:
- Detects user access level
- Renders appropriate interface
- Filters data based on permissions

## Troubleshooting

### Common Issues

1. **Owner can't access admin dashboard**
   - Verify user email exists in `primary_name_owners` table
   - Check spelling of email address
   - Ensure user has logged in at least once

2. **Owner sees no data**
   - Verify contacts exist with the owned primary name
   - Check that contact names follow format: `name@{primary_name}`

3. **Permission denied errors**
   - Check that the user is trying to access data within their namespace
   - Verify database table permissions

### Debugging

Check user access level:
```sql
SELECT 
  po.user_email,
  po.owner_of,
  COUNT(c.name) as contact_count
FROM primary_name_owners po
LEFT JOIN contact c ON c.name LIKE '%@' || po.owner_of
WHERE po.user_email = '<EMAIL>'
GROUP BY po.user_email, po.owner_of;
```

## Best Practices

1. **Email Accuracy**: Ensure email addresses match exactly with OAuth provider emails
2. **Namespace Planning**: Choose clear, meaningful primary names
3. **Regular Audits**: Periodically review ownership assignments
4. **Documentation**: Keep track of who owns which namespaces
5. **Testing**: Test access levels after making changes

## Migration from Existing System

If you're adding this feature to an existing ODude Names installation:

1. Run the database migration script
2. Assign initial ownership as needed
3. Test with a non-super-admin user
4. Verify filtering works correctly
5. Update any custom admin tools if applicable

## Support

For issues with the Primary Name Ownership feature:
1. Check the troubleshooting section above
2. Verify database table structure
3. Review server logs for authentication errors
4. Test with a fresh user account if needed
