:root {
  --white: #ffffff;
  --gray-50: #f8f8f8;
  --gray-100: #f0f0f0;
  --gray-200: #f4f4f5;
  --gray-300: #e2e2e2;
  --gray-600: #6f6f6f;
  --gray-800: #363636;
  --gray-900: #24292e;
  --blue-600: #127af2;
  
  /* Dark theme colors */
  --dark-bg: #1A1B1E;
  --dark-card: #25262b;
  --dark-text: #C1C2C5;
  --dark-border: #373A40;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  @media (max-width: 1080px) {
    font-size: 93.75%; // 15px for tablets
  }

  @media (max-width: 720px) {
    font-size: 87.5%; // 14px for smartphones
  }
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

button {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 8px;
  padding: 0.85rem 2rem;
  font-size: 0.85rem;
  font-family: var(--font-inter);
  // Removed 'all: unset;' to prevent overriding Mantine styles
}
