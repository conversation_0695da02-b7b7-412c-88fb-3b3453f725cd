'use client';

import { Container, Title, Text, Stack, Paper } from '@mantine/core';
import { AdSenseBanner } from 'src/components/AdSense';
import { SimpleAdSense } from 'src/components/AdSense/SimpleAdSense';

export default function TestAdSensePage() {
  return (
    <Container size="md" py="xl">
      <Paper shadow="sm" p="xl" radius="md">
        <Stack gap="lg">
          <div style={{ textAlign: 'center' }}>
            <Title order={1} mb="sm">
              AdSense Test Page
            </Title>
            <Text size="lg" c="dimmed">
              Testing AdSense banner display
            </Text>
          </div>

          <div style={{ border: '2px dashed #ccc', padding: '20px', textAlign: 'center' }}>
            <Text size="sm" c="dimmed" mb="md">
              AdSense Banner Test (468x60) with google official slot 6300978111
            </Text>
            <AdSenseBanner slot="6300978111" responsive={false} width={468} height={60} />
          </div>

          <div style={{ border: '2px dashed #ccc', padding: '20px', textAlign: 'center' }}>
            <Text size="sm" c="dimmed" mb="md">
              AdSense Banner Test (Responsive)
            </Text>
            <AdSenseBanner slot="9822011476" responsive={true} />
          </div>

          <div style={{ border: '2px dashed #ccc', padding: '20px', textAlign: 'center' }}>
            <Text size="sm" c="dimmed" mb="md">
              AdSense Banner Test (300x250)
            </Text>
            <AdSenseBanner slot="4503599449" responsive={false} width={300} height={250} />
          </div>

          <div style={{ border: '2px dashed #ccc', padding: '20px', textAlign: 'center' }}>
            <Text size="sm" c="dimmed" mb="md">
              Simple AdSense Test (300x250)
            </Text>
            <SimpleAdSense slot="4503599449" width={300} height={250} />
          </div>
        </Stack>
      </Paper>
    </Container>
  );
}
