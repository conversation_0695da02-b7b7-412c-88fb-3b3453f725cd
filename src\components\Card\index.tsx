import { PropsWithChildren } from "react";
import { Paper,Divider } from "@mantine/core";

// Remove the SCSS import:
// import styles from "./index.module.scss";

export const Card = ({ children }: PropsWithChildren) => {
  return (
    
    <Paper withBorder 
      shadow="xl" // <PERSON><PERSON>'s 'xl' shadow is close to your custom shadow.
                 // For an exact match: style={{ boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)' }}
      radius={16} // Explicitly set radius to 16px
      style={{
        width: '40rem',
        withBorder: true,
        minHeight: '30rem',
        overflow: 'hidden',
        // Mantine Paper is typically white by default based on theme,
        // but you can explicitly set it if needed:
        // backgroundColor: 'var(--white)', // Or use Mantine theme colors e.g. theme.colors.gray[0]
      }}
    >
  
      {children}
    </Paper>
  );
};
