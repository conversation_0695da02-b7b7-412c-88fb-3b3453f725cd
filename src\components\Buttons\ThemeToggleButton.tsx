'use client';

import { ActionIcon, useComputedColorScheme, useMantineColorScheme } from '@mantine/core';
import { IconMoon, IconSun } from '@tabler/icons-react';
import { useState, useEffect } from 'react';

export function ThemeToggle() {
  const { setColorScheme } = useMantineColorScheme();
  const [mounted, setMounted] = useState(false);
  const computedColorScheme = useComputedColorScheme('light');

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleColorScheme = () => {
    setColorScheme(computedColorScheme === 'dark' ? 'light' : 'dark');
  };

  if (!mounted) {
    return null;
  }

  return (
    <ActionIcon 
      onClick={toggleColorScheme} 
      variant="outline" 
      size="lg"
      radius="md"
      aria-label="Toggle color scheme"
    >
      {computedColorScheme === 'dark' ? (
        <IconSun size={20} stroke={1.5} />
      ) : (
        <IconMoon size={20} stroke={1.5} />
      )}
    </ActionIcon>
  );
}