// Simple event system for points updates
type PointsEventListener = () => void;

class PointsEventManager {
  private listeners: PointsEventListener[] = [];

  subscribe(listener: PointsEventListener): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  emit(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in points event listener:', error);
      }
    });
  }
}

export const pointsEventManager = new PointsEventManager();

// Helper function to trigger points refresh
export const triggerPointsRefresh = () => {
  pointsEventManager.emit();
};
