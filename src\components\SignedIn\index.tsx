"use client"

import { useSession } from "next-auth/react"
import { UserInfo } from "../Card/user";
import {ActionsGrid } from "../Card/ActionsGrid";

import { Divider, Grid } from '@mantine/core';
import { fetchData } from '../../lib/supabase';
import { useEffect, useState } from "react";
import { getUserValue, setUserValue } from '../../lib/common';
import { getUserContactLimit } from '../../lib/user-settings';
import { AdSenseBanner } from "../AdSense";


export const SignedIn = () => {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<any[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [userContactLimit, setUserContactLimit] = useState<number>(4); // Default fallback

  const fetchContacts = async () => {
    if (session?.user?.email) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile',
        filter: [{
          column: 'profile_email',
          value: session.user.email
        }]
      });

      if (!error && data) {
        setContacts(data as any[]);
      }
    }
  };

  const fetchUserContactLimit = async () => {
    if (session?.user?.email) {
      try {
        const limit = await getUserContactLimit(session.user.email);
        setUserContactLimit(limit);
      } catch (error) {
        console.error('Error fetching user contact limit:', error);
        // Keep default value of 4
      }
    }
  };

  const triggerRefresh = () => {
    fetchContacts();
    setRefreshTrigger(prev => prev + 1); // This will trigger ActionsGrid to refresh
  };

  useEffect(() => {
    fetchContacts();
    fetchUserContactLimit();
  }, [session]);

  const saveUserDataToLocalStorage = async () => {
    if (session) {
      const { name: full_name, image: avatar_url } = session?.user || {};
      const { email } = session?.user || {};

      // Only handle localStorage operations here
      // Profile creation is now handled in NextAuth callback
      if (getUserValue(email,'full_name') == null) {
        console.log('Set local storage');
        setUserValue(email, 'full_name', full_name || '');
        setUserValue(email, 'avatar_url', avatar_url || '');
        window.dispatchEvent(new Event('userDataUpdated'));
      }
    }
  };

  useEffect(() => {
    saveUserDataToLocalStorage();
  }, [session]);

  return (
    <>
      <div style={{ maxHeight: '28rem', overflowY: 'auto', padding: '0 1rem' }}>
        <ActionsGrid onContactCreated={fetchContacts} refreshTrigger={refreshTrigger}/>
        <Divider id="div-label" my="xs" label={`My ODude Names (${contacts.length}/${userContactLimit})`} labelPosition="center" w="100%" />

        <Grid>
          {contacts.map((contact) => (
            <Grid.Col key={contact.name} span={6}><UserInfo name={contact.name} /></Grid.Col>
          ))}
        </Grid>
      </div>
    </>
  )
}


