'use client';

import { useEffect, useState } from 'react';
import { Text, Box } from '@mantine/core';
import { IconCoins } from '@tabler/icons-react';
import { createPortal } from 'react-dom';

interface FlyingPointsProps {
  points: number;
  show: boolean;
  onComplete: () => void;
  startPosition?: { x: number; y: number };
}

export function FlyingPoints({ points, show, onComplete, startPosition }: FlyingPointsProps) {
  const [mounted, setMounted] = useState(false);
  const [animationClass, setAnimationClass] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (show) {
      setAnimationClass('flying-points-enter');
      
      const timer = setTimeout(() => {
        setAnimationClass('flying-points-exit');
        
        const exitTimer = setTimeout(() => {
          onComplete();
        }, 500);
        
        return () => clearTimeout(exitTimer);
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, [show, onComplete]);

  if (!mounted || !show) return null;

  const isPositive = points > 0;
  const displayPoints = Math.abs(points);

  const animationStyles = `
    .flying-points-enter {
      animation: flyUp 2s ease-out forwards;
    }
    
    .flying-points-exit {
      animation: fadeOut 0.5s ease-out forwards;
    }
    
    @keyframes flyUp {
      0% {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
      50% {
        transform: translateY(-100px) scale(1.2);
        opacity: 1;
      }
      100% {
        transform: translateY(-200px) scale(1);
        opacity: 0.8;
      }
    }
    
    @keyframes fadeOut {
      0% {
        opacity: 0.8;
      }
      100% {
        opacity: 0;
      }
    }
  `;

  const content = (
    <>
      <style>{animationStyles}</style>
      <Box
        style={{
          position: 'fixed',
          top: startPosition?.y || '50%',
          left: startPosition?.x || '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          pointerEvents: 'none',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '12px 20px',
          backgroundColor: isPositive ? 'var(--mantine-color-green-6)' : 'var(--mantine-color-red-6)',
          color: 'white',
          borderRadius: '25px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
          fontSize: '24px',
          fontWeight: 'bold',
        }}
        className={animationClass}
      >
        <IconCoins size={28} />
        <Text size="xl" fw={700} c="white">
          {isPositive ? '+' : '-'}{displayPoints}
        </Text>
      </Box>
    </>
  );

  return createPortal(content, document.body);
}

// Hook to manage flying points animations
export function useFlyingPoints() {
  const [animations, setAnimations] = useState<Array<{
    id: string;
    points: number;
    startPosition?: { x: number; y: number };
  }>>([]);

  const showFlyingPoints = (points: number, startPosition?: { x: number; y: number }) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    setAnimations(prev => [...prev, { id, points, startPosition }]);
  };

  const removeAnimation = (id: string) => {
    setAnimations(prev => prev.filter(anim => anim.id !== id));
  };

  const FlyingPointsContainer = () => (
    <>
      {animations.map(animation => (
        <FlyingPoints
          key={animation.id}
          points={animation.points}
          show={true}
          onComplete={() => removeAnimation(animation.id)}
          startPosition={animation.startPosition}
        />
      ))}
    </>
  );

  return {
    showFlyingPoints,
    FlyingPointsContainer,
  };
}
