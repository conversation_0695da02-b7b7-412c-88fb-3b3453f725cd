/**
 * Tests for database utility functions
 * Run with: npm test or jest
 */

import {
  getSocialData,
  getCryptoData,
  getNotesData,
  prepareContactData,
  hasSocialData,
  hasCryptoData,
  hasNotesData,
  getSocialUrl,
  validateSocial<PERSON><PERSON><PERSON>,
  validateCryptoAddress,
  createEmptySocialData,
  createEmptyCryptoData,
  createEmptyNotesData
} from '../database-utils';

// Mock data for testing
const contactDataWithSocial = {
  name: 'test',
  social: {
    twitter: 'testuser',
    telegram: 'testuser',
    discord: 'testuser#1234'
  },
  crypto: {
    eth: '0x*********0*********0*********0*********0',
    btc: '**********************************'
  },
  notes: {
    'Bank Details': 'Account: *********',
    'Address': '123 Main St'
  }
};

const contactDataEmpty = {
  name: 'test'
};

describe('Database Utils', () => {
  describe('createEmptySocialData', () => {
    it('should create empty social data structure', () => {
      const result = createEmptySocialData();
      expect(result).toHaveProperty('twitter');
      expect(result).toHaveProperty('telegram');
      expect(result).toHaveProperty('youtube');
      expect(result).toHaveProperty('instagram');
      expect(result).toHaveProperty('facebook');
      expect(result).toHaveProperty('discord');
    });
  });

  describe('createEmptyCryptoData', () => {
    it('should create empty crypto data structure', () => {
      const result = createEmptyCryptoData();
      expect(result).toHaveProperty('eth');
      expect(result).toHaveProperty('bsc');
      expect(result).toHaveProperty('matic');
      expect(result).toHaveProperty('btc');
      expect(result).toHaveProperty('fil');
      expect(result).toHaveProperty('sol');
    });
  });

  describe('createEmptyNotesData', () => {
    it('should create empty notes data structure', () => {
      const result = createEmptyNotesData();
      expect(result).toEqual({});
    });
  });

  describe('getSocialData', () => {
    it('should return social data when available', () => {
      const result = getSocialData(contactDataWithSocial);
      expect(result).toEqual(contactDataWithSocial.social);
    });

    it('should return empty object when no social data', () => {
      const result = getSocialData(contactDataEmpty);
      expect(result).toEqual({});
    });
  });

  describe('getCryptoData', () => {
    it('should return crypto data when available', () => {
      const result = getCryptoData(contactDataWithSocial);
      expect(result).toEqual(contactDataWithSocial.crypto);
    });

    it('should return empty object when no crypto data', () => {
      const result = getCryptoData(contactDataEmpty);
      expect(result).toEqual({});
    });
  });

  describe('getNotesData', () => {
    it('should return notes data when available', () => {
      const result = getNotesData(contactDataWithSocial);
      expect(result).toEqual(contactDataWithSocial.notes);
    });

    it('should return empty object when no notes data', () => {
      const result = getNotesData(contactDataEmpty);
      expect(result).toEqual({});
    });
  });

  describe('hasSocialData', () => {
    it('should return true when social data exists', () => {
      expect(hasSocialData(contactDataWithSocial)).toBe(true);
    });

    it('should return false when no social data exists', () => {
      expect(hasSocialData(contactDataEmpty)).toBe(false);
    });
  });

  describe('hasCryptoData', () => {
    it('should return true when crypto data exists', () => {
      expect(hasCryptoData(contactDataWithSocial)).toBe(true);
    });

    it('should return false when no crypto data exists', () => {
      expect(hasCryptoData(contactDataEmpty)).toBe(false);
    });
  });

  describe('hasNotesData', () => {
    it('should return true when notes data exists', () => {
      expect(hasNotesData(contactDataWithSocial)).toBe(true);
    });

    it('should return false when no notes data exists', () => {
      expect(hasNotesData(contactDataEmpty)).toBe(false);
    });
  });

  describe('getSocialUrl', () => {
    it('should generate correct URLs for different platforms', () => {
      expect(getSocialUrl('twitter', 'testuser')).toBe('https://x.com/testuser');
      expect(getSocialUrl('telegram', 'testuser')).toBe('https://t.me/testuser');
      expect(getSocialUrl('instagram', 'testuser')).toBe('https://instagram.com/testuser');
    });

    it('should handle @ symbols', () => {
      expect(getSocialUrl('twitter', '@testuser')).toBe('https://x.com/testuser');
    });

    it('should handle full URLs', () => {
      expect(getSocialUrl('youtube', 'https://youtube.com/@testuser')).toBe('https://youtube.com/@testuser');
    });
  });

  describe('validateSocialHandle', () => {
    it('should validate Twitter handles correctly', () => {
      expect(validateSocialHandle('twitter', 'validuser')).toBeNull();
      expect(validateSocialHandle('twitter', 'a'.repeat(16))).toContain('1-15 characters');
    });

    it('should validate Telegram handles correctly', () => {
      expect(validateSocialHandle('telegram', 'validuser')).toBeNull();
      expect(validateSocialHandle('telegram', 'abc')).toContain('5-32 characters');
    });
  });

  describe('validateCryptoAddress', () => {
    it('should validate Ethereum addresses correctly', () => {
      expect(validateCryptoAddress('eth', '0x*********0*********0*********0*********0')).toBeNull();
      expect(validateCryptoAddress('eth', 'invalid')).toContain('0x followed by 40 hexadecimal');
    });

    it('should validate Bitcoin addresses correctly', () => {
      expect(validateCryptoAddress('btc', '**********************************')).toBeNull();
      expect(validateCryptoAddress('btc', 'invalid')).toContain('Invalid Bitcoin address');
    });
  });

  describe('prepareContactData', () => {
    it('should prepare data for database insertion', () => {
      const result = prepareContactData(contactDataWithSocial);

      // Should have social, crypto, and notes data
      expect(result.social).toEqual(contactDataWithSocial.social);
      expect(result.crypto).toEqual(contactDataWithSocial.crypto);
      expect(result.notes).toEqual(contactDataWithSocial.notes);

      // Should preserve other fields
      expect(result.name).toBe('test');
    });

    it('should handle empty social/crypto/notes data', () => {
      const result = prepareContactData(contactDataEmpty);

      // Should set null for empty data
      expect(result.social).toBeNull();
      expect(result.crypto).toBeNull();
      expect(result.notes).toBeNull();

      // Should preserve other fields
      expect(result.name).toBe('test');
    });
  });
});
