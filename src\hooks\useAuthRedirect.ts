"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

interface UseAuthRedirectOptions {
  redirectTo?: string
  redirectOnUnauthenticated?: boolean
}

export const useAuthRedirect = (options: UseAuthRedirectOptions = {}) => {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const {
    redirectTo = "/",
    redirectOnUnauthenticated = true
  } = options

  useEffect(() => {
    // Don't redirect while session is still loading
    if (status === 'loading') return

    // Redirect unauthenticated users if enabled
    if (!session && redirectOnUnauthenticated) {
      router.push(redirectTo)
    }
  }, [session, status, router, redirectTo, redirectOnUnauthenticated])

  return {
    session,
    status,
    isAuthenticated: !!session,
    isLoading: status === 'loading'
  }
}
