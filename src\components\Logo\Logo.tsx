'use client';

import Image from 'next/image';
import { Box, useComputedColorScheme } from '@mantine/core';
import { useState, useEffect } from 'react';

export function Logo() {
  const [mounted, setMounted] = useState(false);
  const computedColorScheme = useComputedColorScheme('light');
  const logoSrc = computedColorScheme === 'dark' ? '/logo_dark.png?' : '/logo.png';

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <Box>
      <Image
        src={logoSrc}
        alt="Logo"
        width={100}
        height={50}
        style={{ objectFit: 'contain' }}
      />
    </Box>
  );
}