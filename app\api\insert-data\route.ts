import { NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';

export async function POST(request: Request) {
  try {
    // Check authentication via NextAuth
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Minimal example: upsert into profiles with email and full_name
    const email = session.user.email;
    const full_name = body?.full_name ?? session.user.name ?? null;

    const supabase = getSupabaseAdminClient();

    const { data, error } = await supabase
      .from('profiles')
      .upsert({ email, full_name })
      .select()
      .single();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, profile: data });
  } catch (err) {
    console.error('insert-data error:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

