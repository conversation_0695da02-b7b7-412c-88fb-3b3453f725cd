/**
 * Tests for AddToMobileContactButton component
 * Tests the vCard generation, Base64 image conversion, and device detection
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AddToMobileContactButton } from '../AddToMobileContactButton';
import { ImageData } from 'src/lib/common';

// Mock the notifications
jest.mock('@mantine/notifications', () => ({
  notifications: {
    show: jest.fn(),
  },
}));

// Mock the database utils
jest.mock('src/lib/database-utils', () => ({
  getSocialData: jest.fn(() => ({ twitter: 'testuser', telegram: 'testuser' })),
  getCryptoData: jest.fn(() => ({ eth: '0x123', btc: '1A1zP1' })),
  getNotesData: jest.fn(() => ({ 'Note 1': 'Test note content' })),
  getSocialUrl: jest.fn((platform, handle) => `https://${platform}.com/${handle}`),
}));

// Mock the common utils
jest.mock('src/lib/common', () => ({
  getImage: jest.fn((images, index) => {
    if (!images) return 'https://example.com/default-avatar.png';
    const imageData = typeof images === 'string' ? JSON.parse(images) : images;
    return imageData[`img${index}`] || 'https://example.com/test-image.jpg';
  }),
}));

// Mock fetch for Base64 conversion
global.fetch = jest.fn();
global.URL.createObjectURL = jest.fn(() => 'blob:test-url');
global.URL.revokeObjectURL = jest.fn();

// Mock FileReader
const mockFileReader = {
  readAsDataURL: jest.fn(),
  result: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A',
  onload: null,
  onerror: null,
};

Object.defineProperty(global, 'FileReader', {
  writable: true,
  value: jest.fn(() => mockFileReader),
});

// Mock navigator
Object.defineProperty(global.navigator, 'userAgent', {
  writable: true,
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
});

Object.defineProperty(global.navigator, 'share', {
  writable: true,
  value: undefined,
});

Object.defineProperty(global.navigator, 'canShare', {
  writable: true,
  value: undefined,
});

Object.defineProperty(global.navigator, 'maxTouchPoints', {
  writable: true,
  value: 0,
});

// Test data
const mockContact = {
  name: 'testuser',
  profile: 'Test User',
  description: 'Test Description',
  email: '<EMAIL>',
  phone: '+1234567890',
  website: 'https://example.com',
  images: {
    img1: 'https://example.com/test-image.jpg',
    img2: 'https://example.com/background.jpg',
  } as ImageData,
  social: { twitter: 'testuser', telegram: 'testuser' },
  crypto: { eth: '0x123', btc: '1A1zP1' },
  notes: { 'Note 1': 'Test note content' },
  links: { 'Website': 'https://example.com' },
  image: null,
  uri: null,
  tg_bot: null,
  web2: null,
  web3: null,
  minted: null,
};

describe('AddToMobileContactButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      blob: () => Promise.resolve(new Blob(['test'], { type: 'image/jpeg' })),
    });
  });

  it('renders the button correctly', () => {
    render(<AddToMobileContactButton contact={mockContact} />);
    expect(screen.getByText('Add to Mobile Contact')).toBeInTheDocument();
  });

  it('does not render when contact is null', () => {
    const { container } = render(<AddToMobileContactButton contact={null} />);
    expect(container.firstChild).toBeNull();
  });

  it('detects desktop correctly', () => {
    // Mock desktop user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      configurable: true,
    });

    render(<AddToMobileContactButton contact={mockContact} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    // Should trigger download for desktop
  });

  it('detects iOS correctly', () => {
    // Mock iOS user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
      configurable: true,
    });

    render(<AddToMobileContactButton contact={mockContact} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    // Should handle iOS-specific logic
  });

  it('detects Android correctly', () => {
    // Mock Android user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
      configurable: true,
    });

    render(<AddToMobileContactButton contact={mockContact} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    // Should handle Android-specific logic
  });

  it('converts image to Base64 successfully', async () => {
    const component = render(<AddToMobileContactButton contact={mockContact} />);
    
    // Simulate successful image conversion
    mockFileReader.onload = () => {
      mockFileReader.result = 'data:image/jpeg;base64,testbase64data';
    };

    const button = screen.getByText('Add to Mobile Contact');
    fireEvent.click(button);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('https://example.com/test-image.jpg');
    });
  });

  it('handles image conversion errors gracefully', async () => {
    (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    render(<AddToMobileContactButton contact={mockContact} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    
    // Should not throw error and continue with vCard generation
    await waitFor(() => {
      expect(fetch).toHaveBeenCalled();
    });
  });

  it('skips default avatar images', async () => {
    const contactWithDefaultAvatar = {
      ...mockContact,
      images: {
        img1: 'https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png',
      } as ImageData,
    };

    render(<AddToMobileContactButton contact={contactWithDefaultAvatar} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    
    // Should not try to fetch the default avatar
    expect(fetch).not.toHaveBeenCalledWith(expect.stringContaining('avatar-2.png'));
  });

  it('handles Web Share API when available', () => {
    // Mock Web Share API
    Object.defineProperty(navigator, 'share', {
      value: jest.fn().mockResolvedValue(undefined),
      configurable: true,
    });
    Object.defineProperty(navigator, 'canShare', {
      value: jest.fn().mockReturnValue(true),
      configurable: true,
    });
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
      configurable: true,
    });

    render(<AddToMobileContactButton contact={mockContact} />);
    const button = screen.getByText('Add to Mobile Contact');
    
    fireEvent.click(button);
    
    // Should attempt to use Web Share API on Android
  });
});
