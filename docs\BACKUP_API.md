# Supabase Backup API

This API endpoint provides automated backup functionality for Supabase tables to an external FTP server.

## Endpoint

```
POST /api/backup
GET /api/backup
```

Both GET and POST methods are supported for easier curl usage.

## Configuration

### Environment Variables Required

Add these to your `.env.local` file:

```env
# Supabase Service Role Key (required for full database access)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# FTP Configuration (required for backup uploads)
FTP_PASSWORD=your_ftp_password
```

### FTP Configuration

The FTP settings are configured in `src/lib/config.ts`:

```typescript
export const FTP_HOST = "home.odude.com";
export const FTP_USER = "home";
export const FTP_PASSWORD = process.env.FTP_PASSWORD || "";
export const FTP_PATH = "/public_html/supabase_backup/";
```

## Usage

### Using curl

```bash
# Trigger backup
curl -X POST http://localhost:3000/api/backup

# Or using GET
curl http://localhost:3000/api/backup
```

### Using cron job

You can set up a cron job to automatically backup your database:

```bash
# Add to crontab (backup daily at 2 AM)
0 2 * * * curl -X POST https://yourdomain.com/api/backup
```

## Response Format

### Success Response

```json
{
  "success": true,
  "message": "Successfully backed up 2 tables",
  "files": [
    "contact_2024-06-16-14-30-25.csv",
    "bookmark_2024-06-16-14-30-26.csv"
  ],
  "timestamp": "2024-06-16T14:30:27.123Z"
}
```

### Error Response

```json
{
  "success": false,
  "message": "Backup process failed: Connection timeout",
  "files": [],
  "timestamp": "2024-06-16T14:30:27.123Z"
}
```

## Backup Process

1. **Table Export**: Each configured table is exported as CSV format
2. **File Naming**: Files are named with format: `{table_name}_{YYYY-MM-DD-HH-mm-ss}.csv`
3. **FTP Upload**: CSV files are uploaded to the configured FTP server
4. **Compatibility**: CSV files are compatible with:
   - MySQL (via phpMyAdmin import)
   - Supabase (via SQL editor or pgAdmin)

## Tables Backed Up

Currently configured tables:
- `contact` - Main contact data
- `bookmark` - User bookmarks

To add more tables, modify the `TABLES_TO_BACKUP` array in `/app/api/backup/route.ts`.

## CSV Format

- Headers are included in the first row
- All values are quoted and escaped properly
- JSON columns are serialized as JSON strings
- NULL values are represented as empty strings
- Compatible with standard CSV parsers

## Security Notes

- The API uses the Supabase service role key for full database access
- No authentication is currently implemented on the endpoint
- Consider adding API key authentication for production use
- FTP credentials are stored in environment variables

## Troubleshooting

### Common Issues

1. **Missing Service Role Key**: Ensure `SUPABASE_SERVICE_ROLE_KEY` is set
2. **FTP Connection Failed**: Check FTP credentials and network connectivity
3. **Permission Denied**: Verify FTP user has write permissions to the backup directory

### Logs

Check the server logs for detailed error messages during backup operations.
