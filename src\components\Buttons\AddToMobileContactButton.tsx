'use client';

import { But<PERSON> } from "@mantine/core";
import { IconDeviceMobile } from "@tabler/icons-react";
import { notifications } from '@mantine/notifications';
import { getImage, ImageData } from 'src/lib/common';

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: Record<string, string> | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: ImageData | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  minted: string | null;
}

interface AddToMobileContactButtonProps {
  contact: ContactItem | null;
}

import { getSocialData, getCryptoData, getNotesData, getSocialUrl } from 'src/lib/database-utils';

export function AddToMobileContactButton({ contact }: AddToMobileContactButtonProps) {

  // Function to convert image URL to Base64
  const convertImageToBase64 = async (imageUrl: string): Promise<string | null> => {
    try {
      // Skip default avatar
      if (imageUrl.includes('avatar-2.png')) {
        console.log('Skipping default avatar for vCard');
        return null;
      }

      console.log('Converting image to Base64:', imageUrl);
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      console.log('Image blob size:', blob.size, 'type:', blob.type);

      // Check if it's a valid image
      if (!blob.type.startsWith('image/')) {
        throw new Error(`Invalid image type: ${blob.type}`);
      }

      // Limit image size for vCard compatibility (max 1MB)
      if (blob.size > 1024 * 1024) {
        console.warn('Image too large for vCard, skipping:', blob.size);
        return null;
      }

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = reader.result as string;
          // Remove the data:image/type;base64, prefix to get just the base64 data
          const base64Data = base64.split(',')[1];
          console.log('Successfully converted image to Base64, length:', base64Data.length);
          resolve(base64Data);
        };
        reader.onerror = () => reject(new Error('Failed to convert image to base64'));
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to base64:', error);
      return null;
    }
  };

  // Function to generate vCard data
  const generateVCard = async (contact: ContactItem): Promise<string> => {
    let vcard = 'BEGIN:VCARD\n';
    vcard += 'VERSION:3.0\n';

    // Name fields
    vcard += `FN:${contact.profile || contact.name}\n`;
    vcard += `N:${contact.profile || contact.name};;;;\n`;

    // Contact photo - use getImage(images, 1) for the contact picture
    if (contact.images) {
      const contactImageUrl = getImage(contact.images, 1);
      // Only add photo if it's not the default avatar
      if (contactImageUrl && !contactImageUrl.includes('avatar-2.png')) {
        try {
          // Convert image to Base64 for better mobile compatibility
          const base64Data = await convertImageToBase64(contactImageUrl);
          if (base64Data) {
            // Use Base64 encoding for iOS/Android compatibility
            vcard += `PHOTO;ENCODING=BASE64;TYPE=JPEG:${base64Data}\n`;
          }
        } catch (error) {
          console.log('Could not add photo to vCard:', error);
        }
      }
    }

    // Contact information - handle multiple phone numbers
    if (contact.phone && contact.phone.trim()) {
      const phoneNumbers = contact.phone.split(/[,\s]+/).filter(phone => phone.trim());
      phoneNumbers.forEach((phone, index) => {
        const cleanPhone = phone.trim();
        if (cleanPhone) {
          // Add type for multiple phones (WORK, HOME, CELL, etc.)
          const phoneType = index === 0 ? 'CELL' : index === 1 ? 'HOME' : 'WORK';
          vcard += `TEL;TYPE=${phoneType}:${cleanPhone}\n`;
        }
      });
    }

    if (contact.email && contact.email.trim()) {
      vcard += `EMAIL:${contact.email}\n`;
    }

    if (contact.website && contact.website.trim()) {
      vcard += `URL:${contact.website}\n`;
    }

    // Organization/Title (using description as title if available)
    if (contact.description && contact.description.trim()) {
      vcard += `TITLE:${contact.description}\n`;
    }

    // Social Media Profiles using proper X-SOCIALPROFILE fields
    const socialData = getSocialData(contact);
    Object.entries(socialData)
      .filter(([platform, handle]) => handle && handle.trim())
      .forEach(([platform, handle]) => {
        vcard += `X-SOCIALPROFILE;type=${platform}:${getSocialUrl(platform, handle)}\n`;
      });

    // Additional URLs for custom links
    if (contact.links && Object.entries(contact.links).some(([key, value]) => key.trim() && value.trim())) {
      Object.entries(contact.links)
        .filter(([name, url]) => name.trim() && url.trim())
        .forEach(([name, url]) => {
          const fullUrl = url.startsWith('http') ? url : `https://${url}`;
          vcard += `URL;X-ABLabel=${name}:${fullUrl}\n`;
        });
    }

    // Notes field - handle JSON notes structure
    const notesData = getNotesData(contact);
    if (Object.keys(notesData).length > 0) {
      // Combine all notes into a single string with titles
      const notesText = Object.entries(notesData)
        .filter(([title, content]) => title.trim() && content.trim())
        .map(([title, content]) => `${title}: ${content}`)
        .join('\\n\\n');

      if (notesText) {
        // Properly escape newlines for vCard format - iOS requires \\n for line breaks
        const escapedNotes = notesText
          .replace(/\r\n/g, '\\n')  // Windows line endings
          .replace(/\n/g, '\\n')    // Unix line endings
          .replace(/\r/g, '\\n');   // Mac line endings
        vcard += `NOTE:${escapedNotes}\n`;
      }
    }

    vcard += 'END:VCARD';
    return vcard;
  };

  // Enhanced device detection functions
  const isMobile = () => {
    // Check for mobile user agents and touch capability
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i;
    const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    return mobileRegex.test(navigator.userAgent) || hasTouchScreen;
  };

  const isIOS = () => {
    // Enhanced iOS detection including iPadOS 13+ which reports as desktop
    const iosRegex = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isPadOS = navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1;
    return iosRegex || isPadOS;
  };

  const isAndroid = () => {
    // More specific Android detection
    return /Android/i.test(navigator.userAgent) && !isIOS();
  };

  const getAndroidVersion = () => {
    const match = navigator.userAgent.match(/Android\s([0-9\.]*)/);
    return match ? parseFloat(match[1]) : 0;
  };

  const supportsWebShare = () => {
    return 'share' in navigator && 'canShare' in navigator;
  };

  // Function to download vCard file (fallback method)
  const downloadVCardFile = async (contact: ContactItem) => {
    try {
      const vCardData = await generateVCard(contact);

      // Platform-specific MIME type optimization
      let mimeType = 'text/vcard;charset=utf-8';
      if (isAndroid()) {
        // Android devices often prefer text/x-vcard for better app recognition
        const androidVersion = getAndroidVersion();
        mimeType = androidVersion >= 10 ? 'text/vcard;charset=utf-8' : 'text/x-vcard;charset=utf-8';
      }

      const blob = new Blob([vCardData], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${contact.name || 'contact'}.vcf`;

      // Add additional attributes for better Android compatibility
      if (isAndroid()) {
        link.setAttribute('type', mimeType);
        link.setAttribute('target', '_blank');
      }

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      const message = isAndroid()
        ? 'Contact file downloaded. Open it from your downloads to add to contacts.'
        : 'Contact file downloaded successfully';

      notifications.show({
        title: 'Success',
        message: message,
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to download contact file',
        color: 'red',
      });
    }
  };

  // Function to add contact to mobile
  const addToMobileContact = async (contact: ContactItem) => {
    try {
      console.log('Device detection:', {
        isMobile: isMobile(),
        isIOS: isIOS(),
        isAndroid: isAndroid(),
        supportsWebShare: supportsWebShare(),
        userAgent: navigator.userAgent
      });

      if (isMobile()) {
        if (isIOS()) {
          // For iOS - use data URL which opens in Contacts app
          const vCardData = await generateVCard(contact);
          const dataUrl = `data:text/vcard;charset=utf-8,${encodeURIComponent(vCardData)}`;
          window.open(dataUrl, '_blank');

          notifications.show({
            title: 'Opening Contacts',
            message: 'Contact will open in your Contacts app',
            color: 'blue',
          });
        } else if (isAndroid()) {
          // For Android - enhanced experience with Web Share API
          try {
            const vCardData = await generateVCard(contact);

            // Try Web Share API first (best experience for modern Android)
            if (supportsWebShare()) {
              try {
                const blob = new Blob([vCardData], { type: 'text/vcard' });
                const file = new File([blob], `${contact.name || 'contact'}.vcf`, {
                  type: 'text/vcard',
                  lastModified: Date.now()
                });

                // Check if we can share files
                if (navigator.canShare && navigator.canShare({ files: [file] })) {
                  await navigator.share({
                    title: `Add ${contact.profile || contact.name} to Contacts`,
                    text: `Contact information for ${contact.profile || contact.name}`,
                    files: [file]
                  });

                  notifications.show({
                    title: 'Contact Shared',
                    message: 'Contact shared successfully via system share',
                    color: 'green',
                  });
                  return;
                } else {
                  // Try sharing without files (fallback for older Android)
                  await navigator.share({
                    title: `Add ${contact.profile || contact.name} to Contacts`,
                    text: `Contact: ${contact.profile || contact.name}${contact.email ? ` - ${contact.email}` : ''}${contact.phone ? ` - ${contact.phone}` : ''}`,
                    url: window.location.href
                  });

                  notifications.show({
                    title: 'Contact Info Shared',
                    message: 'Contact information shared. You can manually add to contacts.',
                    color: 'blue',
                  });
                  return;
                }
              } catch (shareError) {
                console.log('Web Share API failed, trying fallback:', shareError);
                // Continue to fallback methods
              }
            }

            // Enhanced fallback: Try to trigger Android's contact import
            const mimeType = 'text/x-vcard';
            const blob = new Blob([vCardData], { type: mimeType });
            const url = URL.createObjectURL(blob);

            // Create a more Android-friendly download
            const link = document.createElement('a');
            link.href = url;
            link.download = `${contact.name || 'contact'}.vcf`;
            link.setAttribute('type', mimeType);
            link.setAttribute('target', '_blank');

            // Add to DOM temporarily
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up
            setTimeout(() => URL.revokeObjectURL(url), 1000);

            notifications.show({
              title: 'Contact Downloaded',
              message: 'Contact file downloaded. Tap the notification or check Downloads to add to contacts.',
              color: 'blue',
            });
          } catch (error) {
            console.error('Android contact handling failed:', error);
            await downloadVCardFile(contact);
          }
        } else {
          // Other mobile browsers - download file
          await downloadVCardFile(contact);
        }
      } else {
        // Desktop - download file
        await downloadVCardFile(contact);
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      // Fallback to download
      await downloadVCardFile(contact);
    }
  };

  // Don't render if no contact
  if (!contact) {
    return null;
  }

  return (
    <Button
      leftSection={<IconDeviceMobile size={16} />}
      variant="filled"
      color="blue"
      size="xs"
      onClick={() => addToMobileContact(contact)}
    >
      Add to Mobile Contact
    </Button>
  );
}
