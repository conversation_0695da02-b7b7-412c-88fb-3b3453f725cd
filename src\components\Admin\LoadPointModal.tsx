'use client';

import { useState } from 'react';
import {
  Modal,
  TextInput,
  NumberInput,
  Textarea,
  Button,
  Group,
  Stack,
  Text,
  Alert,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconCoins, IconAlertCircle } from '@tabler/icons-react';
import { updateUserPoints, getContactOwnerEmail } from '../../lib/points';
import { triggerPointsRefresh } from '../../lib/pointsEvents';

interface LoadPointModalProps {
  opened: boolean;
  onClose: () => void;
}

interface FormValues {
  odudeName: string;
  points: number;
  description: string;
}

export function LoadPointModal({ opened, onClose }: LoadPointModalProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm<FormValues>({
    initialValues: {
      odudeName: '',
      points: 0,
      description: '',
    },
    validate: {
      odudeName: (value) => {
        if (!value.trim()) return 'ODude name is required';
        // Basic format validation - will be validated against database on submit
        return null;
      },
      points: (value) => {
        if (value === 0) return 'Points amount cannot be zero';
        if (Math.abs(value) > 100000) return 'Points amount cannot exceed 100,000';
        return null;
      },
      description: (value) => {
        if (!value.trim()) return 'Description is required';
        if (value.length > 200) return 'Description must be less than 200 characters';
        return null;
      },
    },
  });

  const handleSubmit = async (values: FormValues, isLoad: boolean) => {
    setLoading(true);
    try {
      // First, validate that the ODude name exists and get the associated email
      const email = await getContactOwnerEmail(values.odudeName.toLowerCase());

      if (!email) {
        notifications.show({
          title: 'Error',
          message: `ODude name "${values.odudeName}" not found or has no associated email.`,
          color: 'red',
        });
        return;
      }

      // Determine points change (positive for LOAD, negative for UNLOAD)
      const pointsChange = isLoad ? Math.abs(values.points) : -Math.abs(values.points);

      // Create transaction description
      const transactionDescription = `Admin ${isLoad ? 'loaded' : 'unloaded'} ${Math.abs(values.points)} points: ${values.description}`;

      // Update user points
      const success = await updateUserPoints(
        email,
        pointsChange,
        isLoad ? 'ADMIN_LOAD' : 'ADMIN_UNLOAD',
        transactionDescription,
        `admin_${Date.now()}`
      );

      if (success) {
        notifications.show({
          title: 'Success',
          message: `Successfully ${isLoad ? 'loaded' : 'unloaded'} ${Math.abs(values.points)} points ${isLoad ? 'to' : 'from'} ${values.odudeName}`,
          color: 'green',
        });
        
        // Trigger points refresh for real-time updates
        triggerPointsRefresh();
        
        // Reset form and close modal
        form.reset();
        onClose();
      } else {
        notifications.show({
          title: 'Error',
          message: `Failed to ${isLoad ? 'load' : 'unload'} points. User may not exist or have insufficient balance.`,
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error managing points:', error);
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group gap="xs" align="center">
          <IconCoins size={20} />
          <Text fw={500}>Load/Unload Points</Text>
        </Group>
      }
      size="md"
      centered
    >
      <form>
        <Stack gap="md">
          <Alert
            icon={<IconAlertCircle size={16} />}
            title="Admin Point Management"
            color="blue"
            variant="light"
          >
            Use this tool to add or remove points from user accounts. 
            LOAD adds points, UNLOAD removes points.
          </Alert>

          <TextInput
            label="ODude Name"
            placeholder="Enter ODude name (e.g., google@info)"
            required
            {...form.getInputProps('odudeName')}
          />

          <NumberInput
            label="Points Amount"
            placeholder="Enter points amount"
            required
            min={1}
            max={100000}
            {...form.getInputProps('points')}
          />

          <Textarea
            label="Description"
            placeholder="Enter reason for point adjustment"
            required
            minRows={3}
            maxRows={5}
            {...form.getInputProps('description')}
          />

          <Group justify="space-between" mt="md">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            
            <Group gap="sm">
              <Button
                color="red"
                onClick={() => handleSubmit(form.values, false)}
                disabled={!form.isValid() || loading}
                loading={loading}
              >
                UNLOAD
              </Button>
              
              <Button
                color="green"
                onClick={() => handleSubmit(form.values, true)}
                disabled={!form.isValid() || loading}
                loading={loading}
              >
                LOAD
              </Button>
            </Group>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
