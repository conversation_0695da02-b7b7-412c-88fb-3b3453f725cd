import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { ADMIN_EMAIL } from 'src/lib/config';

export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { passkey } = await request.json();

    if (!passkey) {
      return NextResponse.json({ error: 'Passkey is required' }, { status: 400 });
    }

    // Get the admin passkey from environment variables (server-side only)
    const ADMIN_PASSKEY = process.env.ADMIN_PASSKEY;

    if (!ADMIN_PASSKEY) {
      console.error('ADMIN_PASSKEY environment variable is not set');
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }

    // Verify the passkey
    const isValid = passkey === ADMIN_PASSKEY;

    if (isValid) {
      return NextResponse.json({ valid: true });
    } else {
      // Add a small delay to prevent brute force attacks
      await new Promise(resolve => setTimeout(resolve, 1000));
      return NextResponse.json({ valid: false }, { status: 401 });
    }

  } catch (error) {
    console.error('Passkey verification error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
