'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { isAdmin } from 'src/lib/admin';
import { isAdminSessionValid } from 'src/lib/adminSession';
import { AdminPasskeyModal } from 'src/components/Admin/AdminPasskeyModal';

export function useAdminActions() {
  const { data: session } = useSession();
  const [showPasskeyModal, setShowPasskeyModal] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  const executeAdminAction = (action: () => void) => {
    // Check if user is admin
    if (!session?.user?.email || !isAdmin(session.user.email)) {
      return;
    }

    // Check if admin session is valid
    if (isAdminSessionValid()) {
      // Execute action immediately
      action();
    } else {
      // Store the action and show passkey modal
      setPendingAction(() => action);
      setShowPasskeyModal(true);
    }
  };

  const handlePasskeySuccess = () => {
    setShowPasskeyModal(false);
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
  };

  const handlePasskeyClose = () => {
    setShowPasskeyModal(false);
    setPendingAction(null);
  };

  const isAdminUser = session?.user?.email && isAdmin(session.user.email);

  return {
    executeAdminAction,
    showPasskeyModal,
    handlePasskeySuccess,
    handlePasskeyClose,
    isAdminUser,
  };
}
