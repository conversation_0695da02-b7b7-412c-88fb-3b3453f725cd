# AdSense Banner Component

The `AdSenseBanner` component provides a flexible way to display Google AdSense ads in your application.

## Configuration

Set up the following environment variables:

```env
# Required
NEXT_PUBLIC_ENABLE_ADSENSE=true
NEXT_PUBLIC_ADSENSE_CLIENT_ID=ca-pub-9660854185566265
```

## Usage

### Responsive Banner (Recommended)
```tsx
import { AdSenseBanner } from 'src/components/AdSense';

// Responsive banner (100vw width, 320px height)
<AdSenseBanner 
  slot="3826444003" 
  responsive={true} 
/>
```

### Fixed Size Banner
```tsx
// Fixed size banner (300x250px)
<AdSenseBanner 
  slot="4503599449" 
  responsive={false} 
  width={300} 
  height={250} 
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `slot` | `string` | - | **Required.** AdSense ad slot ID |
| `width` | `number \| string` | `'100vw'` | Banner width |
| `height` | `number` | `320` | Banner height in pixels |
| `responsive` | `boolean` | `true` | Enable responsive behavior |
| `className` | `string` | - | Additional CSS classes |

## Examples

### Homepage Banner
```tsx
<AdSenseBanner slot="3826444003" responsive={true} />
```

### Sidebar Ad
```tsx
<AdSenseBanner 
  slot="4503599449" 
  responsive={false} 
  width={300} 
  height={250} 
/>
```

### Custom Styling
```tsx
<AdSenseBanner 
  slot="3826444003" 
  responsive={true}
  className="my-custom-ad-style"
/>
```

## Notes

- The component automatically handles AdSense initialization
- Ads will not render if `NEXT_PUBLIC_ENABLE_ADSENSE` is false
- Ads will not render if `NEXT_PUBLIC_ADSENSE_CLIENT_ID` is not set
- Responsive ads use `data-auto-format="rspv"` and `data-full-width=""` attributes
- Fixed size ads use the specified width and height dimensions
