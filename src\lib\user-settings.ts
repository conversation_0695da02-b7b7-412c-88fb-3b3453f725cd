import { getSupabase<PERSON>lient } from './supabase';
import { MAX_CONTACTS_LIMIT } from './config';

export interface UserSettings {
  email: string;
  max_contact_limit: number;
  created_at: string;
  updated_at: string;
}

/**
 * Get user's contact limit from settings table
 * If no record exists, returns the default limit from config
 */
export async function getUserContactLimit(userEmail: string): Promise<number> {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('settings')
      .select('max_contact_limit')
      .eq('email', userEmail)
      .single();

    if (error) {
      // If no record found, return default limit
      if (error.code === 'PGRST116') {
        return MAX_CONTACTS_LIMIT;
      }
      console.error('Error fetching user contact limit:', error);
      return MAX_CONTACTS_LIMIT;
    }

    return data?.max_contact_limit || MAX_CONTACTS_LIMIT;
  } catch (error) {
    console.error('Error in getUserContactLimit:', error);
    return MAX_CONTACTS_LIMIT;
  }
}

/**
 * Set user's contact limit in settings table
 */
export async function setUserContactLimit(userEmail: string, limit: number): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('settings')
      .upsert({
        email: userEmail,
        max_contact_limit: limit,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'email'
      });

    if (error) {
      console.error('Error setting user contact limit:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in setUserContactLimit:', error);
    return false;
  }
}

/**
 * Get all user settings
 */
export async function getUserSettings(userEmail: string): Promise<UserSettings | null> {
  try {
    const supabase = getSupabaseClient();
    
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .eq('email', userEmail)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No record found, return null
        return null;
      }
      console.error('Error fetching user settings:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getUserSettings:', error);
    return null;
  }
}

/**
 * Create or update user settings
 */
export async function upsertUserSettings(settings: Partial<UserSettings> & { email: string }): Promise<boolean> {
  try {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from('settings')
      .upsert({
        ...settings,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'email'
      });

    if (error) {
      console.error('Error upserting user settings:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in upsertUserSettings:', error);
    return false;
  }
}
