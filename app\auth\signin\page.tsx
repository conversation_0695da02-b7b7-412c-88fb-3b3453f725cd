'use client';

import { useState, useEffect } from 'react';
import { signIn, getProviders } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Stack,
  Group,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconBrandGoogle,
  IconBrandGithub,
  IconBrandFacebook,
  IconAlertCircle,
  IconBan,
} from '@tabler/icons-react';
import FullLayout from 'src/components/layouts/FullLayout';

interface Provider {
  id: string;
  name: string;
  type: string;
  signinUrl: string;
  callbackUrl: string;
}

export default function SignInPage() {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null);
  const [loading, setLoading] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders();
      setProviders(res);
    };
    fetchProviders();
  }, []);

  const handleSignIn = async (providerId: string) => {
    setLoading(providerId);
    try {
      await signIn(providerId, { callbackUrl: '/' });
    } catch (error) {
      console.error('Sign in error:', error);
    } finally {
      setLoading(null);
    }
  };

  const getProviderIcon = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return <IconBrandGoogle size={20} />;
      case 'github':
        return <IconBrandGithub size={20} />;
      case 'facebook':
        return <IconBrandFacebook size={20} />;
      default:
        return null;
    }
  };

  const getProviderColor = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return 'red';
      case 'github':
        return 'dark';
      case 'facebook':
        return 'blue';
      default:
        return 'blue';
    }
  };

  return (
    <FullLayout>
      <Container size="sm" py="xl">
        <Paper withBorder shadow="md" p="xl" radius="md">
          <Stack gap="lg">
            <div style={{ textAlign: 'center' }}>
              <Title order={2} mb="sm">
                Sign in to ODude
              </Title>
              <Text size="sm" c="dimmed">
                Choose your preferred sign-in method
              </Text>
            </div>

            {error && (
              <Alert
                icon={error === 'AccessDenied' ? <IconBan size={16} /> : <IconAlertCircle size={16} />}
                title={error === 'AccessDenied' ? 'Account Disabled' : 'Sign In Error'}
                color={error === 'AccessDenied' ? 'orange' : 'red'}
                variant="light"
              >
                {error === 'AccessDenied' ? (
                  <div>
                    <Text size="sm">
                      Your account has been disabled by an administrator. 
                      You cannot sign in at this time.
                    </Text>
                    <Text size="sm" mt="xs">
                      If you believe this is an error, please contact support at{' '}
                      <Text component="span" fw={500}><EMAIL></Text>
                    </Text>
                  </div>
                ) : error === 'OAuthSignin' ? (
                  'Error occurred during sign in. Please try again.'
                ) : error === 'OAuthCallback' ? (
                  'Error occurred during authentication callback. Please try again.'
                ) : error === 'OAuthCreateAccount' ? (
                  'Could not create account. Please try again.'
                ) : error === 'EmailCreateAccount' ? (
                  'Could not create account with email. Please try again.'
                ) : error === 'Callback' ? (
                  'Error occurred during callback. Please try again.'
                ) : error === 'OAuthAccountNotLinked' ? (
                  'Account is already linked to another provider. Please use the original sign-in method.'
                ) : error === 'EmailSignin' ? (
                  'Error sending email. Please try again.'
                ) : error === 'CredentialsSignin' ? (
                  'Invalid credentials. Please check your information and try again.'
                ) : error === 'SessionRequired' ? (
                  'You must be signed in to access this page.'
                ) : (
                  'An unexpected error occurred. Please try again.'
                )}
              </Alert>
            )}

            {error !== 'AccessDenied' && (
              <>
                <Divider label="Sign in with" labelPosition="center" />

                <Stack gap="md">
                  {providers &&
                    Object.values(providers).map((provider) => (
                      <Button
                        key={provider.name}
                        variant="outline"
                        size="md"
                        leftSection={getProviderIcon(provider.id)}
                        onClick={() => handleSignIn(provider.id)}
                        loading={loading === provider.id}
                        color={getProviderColor(provider.id)}
                        fullWidth
                      >
                        Continue with {provider.name}
                      </Button>
                    ))}
                </Stack>
              </>
            )}

            <Divider />

            <div style={{ textAlign: 'center' }}>
              <Text size="xs" c="dimmed">
                By signing in, you agree to our terms of service and privacy policy.
                ODude Names are decentralized and give you full control over your data.
              </Text>
            </div>
          </Stack>
        </Paper>
      </Container>
    </FullLayout>
  );
}
