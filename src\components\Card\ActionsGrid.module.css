.card {
  background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
}

.title {
  font-family: Outfit, var(--mantine-font-family);
  font-weight: 500;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: var(--mantine-radius-md);
  height: 70px;
  background-color: var(--mantine-color-body);
  transition:
    box-shadow 150ms ease,
    transform 100ms ease;

  &:hover {
    box-shadow: var(--mantine-shadow-md);
    transform: scale(1.05);
  }
}

.wideItem {
  width: 100%;
  height: 40px;
}

.bookmarkItem {
  background: linear-gradient(135deg, var(--mantine-color-pink-0), var(--mantine-color-pink-1));
  border: 1px solid var(--mantine-color-pink-3);

  &:hover {
    background: linear-gradient(135deg, var(--mantine-color-pink-1), var(--mantine-color-pink-2));
    box-shadow: var(--mantine-shadow-lg);
    transform: scale(1.08);
  }
}
