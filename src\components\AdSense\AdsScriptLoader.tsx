'use client';
import { useEffect } from 'react';
import { ENABLE_ADSENSE, ADSENSE_CLIENT_ID } from 'src/lib/config';

export default function AdsScriptLoader() {
  useEffect(() => {
    if (document.getElementById('adsbygoogle-js')) return;
    const script = document.createElement('script');
    script.id = 'adsbygoogle-js';
    script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9660854185566265';
    script.async = true;
    script.crossOrigin = 'anonymous';
    document.head.appendChild(script);
  }, []);
  return null;
}
