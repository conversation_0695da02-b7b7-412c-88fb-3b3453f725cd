# ODude Assets System - Developer Documentation

## Overview

The ODude Assets System is a comprehensive digital asset management platform that allows users to create, send, receive, and showcase digital assets like badges, certificates, tickets, and coupons. This system provides full control to both asset creators (owners) and recipients.

## Quick Setup

### 1. Database Setup (Fresh Installation)

Run the fresh database setup script:

```bash
# Execute the complete database setup
psql -d your_database -f scripts/create-assets-tables.sql
```

This script creates:
- All core tables (profiles, contact, primary_name_owners, etc.)
- Assets system tables (assets, asset_transfers, asset_templates)
- Points system and transaction logging
- QR services system
- All necessary indexes and views
- Default asset templates

### 2. Fix Asset Creation Issue

The "Image is required, Issuer ODude name is required" error occurs because the system tries to get the user's contact name, but users might not have created any contacts yet. The fix is to check for primary name ownership instead.

**Problem**: The `getUserOwnContactName` function looks for contacts owned by the user, but for asset creation, we need to check if the user owns any primary names (TLNs).

**Solution**: Update the asset creation logic to use primary name ownership.

## System Architecture

### Database Schema

#### Core Tables
- **`profiles`** - User authentication and points system
- **`contact`** - ODude Names with all profile information  
- **`primary_name_owners`** - Primary name ownership assignments
- **`transaction_log`** - Points transaction history
- **`user_qr_services`** - QR services configuration per contact

#### Assets Tables
- **`assets`** - Main assets table with metadata
- **`asset_transfers`** - Asset transfer tracking with approval status
- **`asset_templates`** - Predefined templates for asset creation

#### Views
- **`assets_with_stats`** - Assets with transfer statistics for dashboards

### Key Features

1. **Asset Creation**: Users can create digital assets with images and metadata
2. **Asset Transfer**: Send assets to other ODude names with approval workflow
3. **Asset Management**: Full CRUD operations for asset owners
4. **Public Showcase**: Public pages for displaying approved assets
5. **Template System**: Predefined templates for consistent asset design
6. **Points Integration**: Asset creation costs points, transfers are free

## Asset Types

- **Badge**: Achievement or recognition badges
- **Certificate**: Professional certificates and credentials  
- **Ticket**: Event tickets and access passes
- **Coupon**: Discount coupons and promotional offers

## API Endpoints

### Assets Management
- `POST /api/assets` - Create new asset
- `GET /api/assets` - Get user's assets (with optional stats)
- `PUT /api/assets/[id]` - Update asset
- `DELETE /api/assets/[id]` - Delete asset (soft delete)

### Asset Transfers
- `POST /api/assets/transfer` - Send asset to another user
- `GET /api/assets/transfer` - Get sent/received transfers
- `POST /api/assets/respond` - Approve/decline/hide received assets

## Frontend Components

### Core Components
- `AssetCreationForm` - Modal for creating new assets
- `AssetLibrary` - Display and manage user's assets
- `AssetTransferModal` - Send assets to other users
- `AssetInbox` - Manage received assets
- `AssetShowcase` - Public display of assets

### Pages
- `/owner` - Owner dashboard with Assets tab
- `/assets` - User's asset inbox for received assets
- `/profile/assets/[id]` - Public asset showcase pages

## Asset Creation Workflow

1. **Authentication Check**: Verify user is logged in
2. **Primary Name Check**: Ensure user owns at least one primary name
3. **Form Validation**: Validate title, type, and image
4. **Image Upload**: Upload to Supabase storage
5. **Database Insert**: Create asset record with metadata
6. **Points Deduction**: Deduct creation cost from user points

## Asset Transfer Workflow

1. **Asset Selection**: Choose asset to transfer
2. **Recipient Validation**: Validate ODude name format
3. **Recipient Lookup**: Check if recipient is registered
4. **Transfer Creation**: Create pending transfer record
5. **Notification**: Notify recipient (if registered)

## Asset Response Workflow

1. **Transfer Lookup**: Find pending transfer
2. **Authorization Check**: Verify recipient permissions
3. **Status Update**: Update transfer status (approved/declined/hidden)
4. **Timestamp**: Record response time

## Points System Integration

- **Asset Creation**: Costs points (configurable amount)
- **Asset Transfer**: Free operation
- **Transaction Logging**: All point changes are logged

## Security Considerations

1. **Ownership Validation**: Only asset owners can transfer/delete
2. **Primary Name Verification**: Users must own primary names to create assets
3. **Image Upload Security**: Validate file types and sizes
4. **SQL Injection Prevention**: Use parameterized queries
5. **XSS Prevention**: Sanitize user inputs

## Error Handling

### Common Issues

1. **"Image is required, Issuer ODude name is required"**
   - **Cause**: User doesn't have primary name ownership
   - **Fix**: Ensure user owns at least one primary name in `primary_name_owners` table

2. **Asset creation fails**
   - **Cause**: Missing required fields or validation errors
   - **Fix**: Check form validation and required fields

3. **Transfer fails**
   - **Cause**: Invalid recipient ODude name or permissions
   - **Fix**: Validate ODude name format and ownership

## Development Guidelines

### Code Organization
```
src/
├── components/Assets/
│   ├── AssetCreationForm.tsx
│   ├── AssetLibrary.tsx
│   ├── AssetTransferModal.tsx
│   └── AssetInbox.tsx
├── lib/
│   ├── assets-utils.ts
│   └── database.types.ts
└── pages/api/assets/
    ├── route.ts
    ├── transfer/route.ts
    └── respond/route.ts
```

### Best Practices

1. **Use TypeScript**: Leverage type safety for asset operations
2. **Error Boundaries**: Implement proper error handling
3. **Loading States**: Show progress during uploads/operations
4. **Optimistic Updates**: Update UI before server confirmation
5. **Caching**: Cache asset data for better performance

## Testing

### Unit Tests
- Asset validation functions
- Utility functions for asset operations
- Database query functions

### Integration Tests  
- Asset creation workflow
- Transfer and response workflows
- API endpoint testing

### E2E Tests
- Complete asset lifecycle
- User interaction flows
- Error scenarios

## Deployment

### Environment Variables
```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Database Migration
For existing databases, run migration scripts in order:
1. `add-primary-name-owners-table.sql`
2. `add-ownership-type-field.sql`  
3. `create-assets-tables.sql`

For fresh installations, use:
```bash
psql -d your_database -f scripts/create-assets-tables.sql
```

## Troubleshooting

### Asset Creation Issues

1. **Check Primary Name Ownership**
   ```sql
   SELECT * FROM primary_name_owners WHERE user_email = '<EMAIL>';
   ```

2. **Verify User Has Points**
   ```sql
   SELECT points FROM profiles WHERE email = '<EMAIL>';
   ```

3. **Check Asset Validation**
   - Ensure all required fields are provided
   - Verify image upload is successful
   - Check asset type is valid

### Transfer Issues

1. **Validate Recipient ODude Name**
   ```sql
   SELECT * FROM contact WHERE name = 'recipient@domain';
   ```

2. **Check Transfer Permissions**
   - Verify sender owns the asset
   - Ensure recipient ODude name exists

## Support

For issues or questions:
1. Check this documentation first
2. Review error logs and console output
3. Verify database schema matches expected structure
4. Test with minimal data to isolate issues

## Future Enhancements

- Asset expiration handling
- Bulk asset operations
- Asset categories and tags
- Advanced template system
- Asset analytics and reporting
