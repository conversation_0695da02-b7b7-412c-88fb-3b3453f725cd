-- Insert Plain template for all asset types (SVG-based)
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
-- Plain Badge template
(
  'plain_badge',
  'Plain Badge Template',
  'Minimal template that displays just the asset image with title',
  'Badge',
  '<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
    <rect width="300" height="300" fill="white" stroke="#e5e7eb" stroke-width="2" rx="10"/>
    <text x="150" y="40" text-anchor="middle" fill="#374151" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="600">
      {{asset_title}}
    </text>
    <text x="150" y="270" text-anchor="middle" fill="#6b7280" font-family="Inter, Arial, sans-serif" font-size="12">
      {{full_name}}
    </text>
    <text x="150" y="290" text-anchor="middle" fill="#9ca3af" font-family="Inter, Arial, sans-serif" font-size="10">
      {{issued_date}}
    </text>
  </svg>',
  true,
  NOW()
),
-- Plain Certificate template
(
  'plain_certificate',
  'Plain Certificate Template',
  'Minimal certificate template with simple border',
  'Certificate',
  '<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300">
    <rect width="400" height="300" fill="white" stroke="#d1d5db" stroke-width="2" rx="5"/>
    <text x="200" y="50" text-anchor="middle" fill="#374151" font-family="Georgia, serif" font-size="20" font-weight="bold">
      CERTIFICATE
    </text>
    <text x="200" y="80" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="14">
      {{asset_title}}
    </text>
    <text x="200" y="130" text-anchor="middle" fill="#374151" font-family="Georgia, serif" font-size="18" font-weight="600">
      {{full_name}}
    </text>
    <text x="200" y="160" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="12">
      {{asset_description}}
    </text>
    <text x="200" y="250" text-anchor="middle" fill="#9ca3af" font-family="Georgia, serif" font-size="10">
      Issued: {{issued_date}} | By: {{profile_name}}
    </text>
  </svg>',
  true,
  NOW()
),
-- Plain Ticket template
(
  'plain_ticket',
  'Plain Ticket Template',
  'Minimal ticket template with simple design',
  'Ticket',
  '<svg xmlns="http://www.w3.org/2000/svg" width="350" height="150" viewBox="0 0 350 150">
    <rect width="350" height="150" fill="white" stroke="#d1d5db" stroke-width="2" rx="5"/>
    <line x1="280" y1="0" x2="280" y2="150" stroke="#d1d5db" stroke-width="1" stroke-dasharray="3,3"/>
    <text x="140" y="40" text-anchor="middle" fill="#374151" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="140" y="70" text-anchor="middle" fill="#6b7280" font-family="Inter, Arial, sans-serif" font-size="12">
      {{asset_description}}
    </text>
    <text x="140" y="100" text-anchor="middle" fill="#374151" font-family="Inter, Arial, sans-serif" font-size="14">
      {{full_name}}
    </text>
    <text x="140" y="130" text-anchor="middle" fill="#9ca3af" font-family="Inter, Arial, sans-serif" font-size="10">
      {{issued_date}}
    </text>
    <text x="315" y="75" text-anchor="middle" fill="#9ca3af" font-family="Inter, Arial, sans-serif" font-size="8" transform="rotate(90 315 75)">
      TICKET
    </text>
  </svg>',
  true,
  NOW()
),
-- Plain Coupon template
(
  'plain_coupon',
  'Plain Coupon Template',
  'Minimal coupon template with simple border',
  'Coupon',
  '<svg xmlns="http://www.w3.org/2000/svg" width="350" height="150" viewBox="0 0 350 150">
    <rect width="350" height="150" fill="white" stroke="#d1d5db" stroke-width="2" stroke-dasharray="5,5" rx="5"/>
    <text x="175" y="40" text-anchor="middle" fill="#374151" font-family="Inter, Arial, sans-serif" font-size="18" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="175" y="70" text-anchor="middle" fill="#6b7280" font-family="Inter, Arial, sans-serif" font-size="14">
      {{asset_description}}
    </text>
    <text x="175" y="100" text-anchor="middle" fill="#374151" font-family="Inter, Arial, sans-serif" font-size="12">
      Valid for: {{full_name}}
    </text>
    <text x="80" y="130" text-anchor="middle" fill="#9ca3af" font-family="Inter, Arial, sans-serif" font-size="10">
      Issued: {{issued_date}}
    </text>
    <text x="270" y="130" text-anchor="middle" fill="#9ca3af" font-family="Inter, Arial, sans-serif" font-size="10">
      Expires: {{expire_date}}
    </text>
  </svg>',
  true,
  NOW()
);
