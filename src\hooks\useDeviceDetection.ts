'use client';

import { useState, useEffect } from 'react';

interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
}

export function useDeviceDetection(): DeviceInfo {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouchDevice: false,
  });

  useEffect(() => {
    const detectDevice = () => {
      if (typeof window === 'undefined' || typeof navigator === 'undefined') {
        return;
      }

      const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // Mobile detection
      const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      
      // Tablet detection (including iPad)
      const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent) || 
                      (isTouchDevice && window.innerWidth >= 768 && window.innerWidth <= 1024);
      
      // Desktop detection
      const isDesktop = !isMobile && !isTablet;

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        isTouchDevice,
      });
    };

    // Initial detection
    detectDevice();

    // Re-detect on window resize (for responsive behavior)
    const handleResize = () => {
      detectDevice();
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return deviceInfo;
}
