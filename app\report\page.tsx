'use client';

import { useState, useEffect } from 'react';
import {
  Title,
  Text,
  Stack,
  TextInput,
  Textarea,
  Button,
  Group,
  Divider,
  Alert,
  Select,
} from '@mantine/core';
import {
  IconUser,
  IconMail,
  IconMessage,
  IconCheck,
  IconX,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import FullLayout from 'src/components/layouts/FullLayout';
import { AdSenseBanner } from 'src/components/AdSense/AdSenseBanner';

export default function ReportPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    reportType: '',
    profileName: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);
  const [mathQuestion, setMathQuestion] = useState({ num1: 0, num2: 0, answer: 0 });
  const [userAnswer, setUserAnswer] = useState<number | string>('');

  // Generate new math question
  const generateMathQuestion = () => {
    const num1 = Math.floor(Math.random() * 9) + 1; // 1-9
    const num2 = Math.floor(Math.random() * 9) + 1; // 1-9
    const answer = num1 + num2;
    setMathQuestion({ num1, num2, answer });
    setUserAnswer('');
  };

  // Generate initial math question
  useEffect(() => {
    generateMathQuestion();
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.reportType || !formData.profileName.trim() || !formData.message.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please fill in all required fields',
        color: 'red',
        icon: <IconX size={16} />
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      notifications.show({
        title: 'Invalid Email',
        message: 'Please enter a valid email address',
        color: 'red',
        icon: <IconX size={16} />
      });
      return;
    }

    // Math captcha validation
    if (Number(userAnswer) !== mathQuestion.answer) {
      notifications.show({
        title: 'Incorrect Answer',
        message: 'Please solve the math problem correctly to verify you are not a robot',
        color: 'red',
        icon: <IconX size={16} />
      });
      generateMathQuestion(); // Generate new question
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch('/api/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          mathAnswer: userAnswer,
          expectedAnswer: mathQuestion.answer
        }),
      });

      if (response.ok) {
        notifications.show({
          title: 'Report Submitted!',
          message: 'Thank you for your report. We will review it and take appropriate action if necessary.',
          color: 'green',
          icon: <IconCheck size={16} />
        });
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          reportType: '',
          profileName: '',
          subject: '',
          message: ''
        });
        generateMathQuestion(); // Generate new question after successful submission
      } else {
        throw new Error('Failed to submit report');
      }
    } catch (error) {
      console.error('Report submission error:', error);
      notifications.show({
        title: 'Submission Failed',
        message: 'There was an error submitting your report. Please try again.',
        color: 'red',
        icon: <IconX size={16} />
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <FullLayout>
      <Stack gap="lg">
        <div style={{ textAlign: 'center' }}>
          <Title order={1} mb="sm">
            Report Profile
          </Title>
          <Text size="lg" c="dimmed">
            Report inappropriate content or claim ownership of a profile. We review all reports carefully.
          </Text>
        
        </div>

        <Divider />

        <Alert color="orange" variant="light" icon={<IconAlertTriangle size={16} />}>
          <Text size="sm">
            <strong>Important:</strong> Please provide accurate information in your report. 
            False reports may result in restrictions on your account. We will investigate all reports 
            and take appropriate action if violations are found.
          </Text>
        </Alert>

        <form onSubmit={handleSubmit}>
          <Stack gap="md">
            <Group grow>
              <TextInput
                label="Your Name"
                placeholder="Your full name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                leftSection={<IconUser size={16} />}
                required
                disabled={loading}
              />
              <TextInput
                label="Your Email"
                placeholder="<EMAIL>"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                leftSection={<IconMail size={16} />}
                required
                disabled={loading}
              />
            </Group>

            <Group grow>
              <Select
                label="Report Type"
                placeholder="Select report type"
                value={formData.reportType}
                onChange={(value) => handleInputChange('reportType', value || '')}
                data={[
                  { value: 'inappropriate_content', label: 'Inappropriate Content' },
                  { value: 'impersonation', label: 'Impersonation' },
                  { value: 'copyright_violation', label: 'Copyright Violation' },
                  { value: 'spam', label: 'Spam' },
                  { value: 'claim_ownership', label: 'Claim Ownership' },
                  { value: 'other', label: 'Other' }
                ]}
                required
                disabled={loading}
              />
              <TextInput
                label="Profile Name to Report"
                placeholder="e.g., name@me"
                value={formData.profileName}
                onChange={(e) => handleInputChange('profileName', e.target.value)}
                leftSection={<IconUser size={16} />}
                required
                disabled={loading}
              />
            </Group>

            <TextInput
              label="Subject (Optional)"
              placeholder="Brief description of the issue"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              leftSection={<IconMessage size={16} />}
              disabled={loading}
            />

            <Textarea
              label="Detailed Report"
              placeholder="Please provide detailed information about your report. Include any relevant evidence or context."
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              minRows={4}
              maxRows={8}
              required
              disabled={loading}
            />

            {/* Math Captcha */}
            <div>
              <Text size="sm" fw={500} mb="xs">
                Security Check: What is {mathQuestion.num1} + {mathQuestion.num2}?
              </Text>
              <TextInput
                placeholder="Enter the answer"
                value={userAnswer}
                onChange={(e) => setUserAnswer(e.target.value)}
                type="number"
                required
                disabled={loading}
                style={{ maxWidth: '200px' }}
              />
            </div>

            <Group justify="center" mt="lg">
              <Button
                type="submit"
                size="md"
                loading={loading}
                leftSection={<IconAlertTriangle size={16} />}
              >
                Submit Report
              </Button>
            </Group>
          </Stack>
        </form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Text size="sm" c="dimmed">
            For urgent matters, you can also contact us <NAME_EMAIL>
          </Text>

            <AdSenseBanner slot="2099365759" responsive={false} width={468} height={60} />
        </div>
      </Stack>
    </FullLayout>
  );
}
