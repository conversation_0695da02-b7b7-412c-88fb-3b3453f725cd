import React, { useState } from 'react';
import { Modal, Image, Grid, Space } from '@mantine/core';

interface ImageGridProps {
  images: string[];
}

const ImageGrid: React.FC<ImageGridProps> = ({ images }) => {
  const [opened, setOpened] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleThumbnailClick = (image: string) => {
    setSelectedImage(image);
    setOpened(true);
  };

  return (
    <div>
        <Space h="md" />
      <Grid >
        {images.slice(2).map((image, index) => (
          <Grid.Col span={3} key={index}>
            <Image
              src={image}
              width={50}
              height={50}
              fit="cover"
              onClick={() => handleThumbnailClick(image)}
              style={{ cursor: 'pointer' }}
              radius="md"
            />
          </Grid.Col>
        ))}
      </Grid>

      <Modal opened={opened} onClose={() => setOpened(false)} size="lg">
        {selectedImage && <Image src={selectedImage} />}
      </Modal>
    </div>
  );
};

export default ImageGrid;