import {
  IconBuildingBank,
  IconCreditCard,
  IconInfoCircle,
  IconArrowsExchange,
} from '@tabler/icons-react';
import {
  Card,
  Group,
  SimpleGrid,
  Text,
  Tooltip,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { notifications } from '@mantine/notifications';
import { fetchData } from '../../lib/supabase';
import { getUserContactLimit } from '../../lib/user-settings';
import { NameModal } from '../Modals/name';
import { TransferPointsModal } from '../Modals/TransferPoints';
import classes from './ActionsGrid.module.css';

const mockdata = [
  { title: 'My Bookmarks', icon: IconCreditCard, color: 'violet', href: '/bookmark' },
  { title: 'Create Name', icon: IconBuildingBank, color: 'indigo' },
  { title: 'Transfer Points', icon: IconArrowsExchange, color: 'orange' },
];

interface ActionsGridProps {
  onContactCreated?: () => void; // Optional callback to refresh parent contacts
  refreshTrigger?: number; // Trigger to refresh local contacts when parent changes
}

export function ActionsGrid({ onContactCreated, refreshTrigger }: ActionsGridProps) {
  const theme = useMantineTheme();
  const router = useRouter();
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<any[]>([]);
  const [nameModalOpened, setNameModalOpened] = useState(false);
  const [transferModalOpened, setTransferModalOpened] = useState(false);
  const [existingNameToEdit, setExistingNameToEdit] = useState<string | undefined>(undefined);

  const fetchContacts = async () => {
    if (session?.user?.email) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile',
        filter: [{
          column: 'profile_email',
          value: session.user.email
        }]
      });

      if (!error && data) {
        setContacts(data as any[]);
      }
    }
  };

  useEffect(() => {
    fetchContacts();
  }, [session]);

  // Refresh local contacts when refreshTrigger changes (when contacts are deleted/updated)
  useEffect(() => {
    if (refreshTrigger !== undefined && refreshTrigger > 0) {
      fetchContacts();
    }
  }, [refreshTrigger]);

  const handleContactSaved = () => {
    // Refresh local contacts state
    fetchContacts();
    // Also refresh parent contacts if callback provided
    if (onContactCreated) {
      onContactCreated();
    }
  };



  const openNewContactModal = async () => {
    if (!session?.user?.email) return;

    try {
      // Get user's contact limit
      const userContactLimit = await getUserContactLimit(session.user.email);

      // Check if limit is reached
      if (contacts.length >= userContactLimit) {
        notifications.show({
          title: 'Creation Limit Reached',
          message: `You can only create a maximum of ${userContactLimit} ODude Names. Please delete an existing name to create a new one.`,
          color: 'orange',
        });
        return;
      }

      setExistingNameToEdit(undefined);
      setNameModalOpened(true);
    } catch (error) {
      console.error('Error checking contact limit:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to check contact limit. Please try again.',
        color: 'red',
      });
    }
  };



  const handleItemClick = (item: typeof mockdata[0]) => {
    if (item.href) {
      router.push(item.href);
    } else if (item.title === 'Create Name') {
      openNewContactModal();
    } else if (item.title === 'Transfer Points') {
      setTransferModalOpened(true);
    }
  };

  const items = mockdata.map((item) => (
    <UnstyledButton
      key={item.title}
      className={classes.item}
      onClick={() => handleItemClick(item)}
      style={{ cursor: (item.href || item.title === 'Create Name' || item.title === 'Transfer Points') ? 'pointer' : 'default' }}
    >
      <item.icon color={theme.colors[item.color][6]} size={32} />
      <Text size="xs" mt={7}>
        {item.title}
      </Text>
    </UnstyledButton>
  ));

  return (
    <>
      <NameModal
        opened={nameModalOpened}
        onClose={() => setNameModalOpened(false)}
        existingName={existingNameToEdit}
        onContactSaved={handleContactSaved}
      />

      <TransferPointsModal
        opened={transferModalOpened}
        onClose={() => setTransferModalOpened(false)}
        onTransferComplete={handleContactSaved}
      />

      <Card withBorder radius="md" className={classes.card}>
        <Group justify="space-between">
          <Text className={classes.title}>{session?.user?.email}</Text>
          <Group gap="xs" align="center">
            <Text size="xs" c="dimmed" style={{ lineHeight: 1 }}>
              ODude Name
            </Text>
            <Tooltip
              label="The name which is full in your control. No one will be able to block or control it, and lifetime free"
              withArrow
              multiline
              w={220}
            >
              <IconInfoCircle
                size={14}
                style={{
                  color: 'var(--mantine-color-dimmed)',
                  cursor: 'help'
                }}
              />
            </Tooltip>
          </Group>
        </Group>
        <SimpleGrid cols={3} mt="md">
          {items}
        </SimpleGrid>
      </Card>
    </>
  );
}