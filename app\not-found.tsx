'use client';

import { Container, Title, Text, Button, Group, Stack } from '@mantine/core';
import { IconHome, IconArrowLeft } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const router = useRouter();

  return (
    <Container size="md" style={{ textAlign: 'center', paddingTop: '4rem', paddingBottom: '4rem' }}>
      <Stack gap="xl" align="center">
        <div>
          <Title 
            order={1} 
            size="6rem" 
            fw={900}
            style={{ 
              lineHeight: 1,
              marginBottom: '1rem'
            }}
          >
            404
          </Title>
          <Title order={2} size="2rem" fw={500} mb="md">
            Page Not Found
          </Title>
          <Text size="lg" c="dimmed" mb="xl">
            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
          </Text>
        </div>

        <Group justify="center" gap="md">
          <Button
            variant="filled"
            size="md"
            leftSection={<IconHome size={20} />}
            onClick={() => router.push('/')}
          >
            Go to Home
          </Button>
          <Button
            variant="outline"
            size="md"
            leftSection={<IconArrowLeft size={20} />}
            onClick={() => router.back()}
          >
            Go Back
          </Button>
        </Group>
      </Stack>
    </Container>
  );
}
