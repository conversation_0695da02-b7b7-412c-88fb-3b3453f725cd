import { useEffect, useState, useRef } from 'react';
import {
  Icon2fa,
  IconBellRinging,
  IconDatabase,
  IconDatabaseImport,
  IconFingerprint,
  IconHeart,
  IconKey,
  IconLogout,
  IconMoon,
  IconReceipt2,
  IconSettings,
  IconShield,
  IconSun,
  IconSwitchHorizontal,
  IconCoins,
  IconHome,
  IconTrophy,
} from '@tabler/icons-react';
import { Avatar, Code, Group, Text, useComputedColorScheme, useMantineColorScheme } from '@mantine/core';
import { useSession } from 'next-auth/react';
import { deleteUserValue, getUserValue } from '../../../lib/common';
import { notifications } from '@mantine/notifications';
import { usePathname, useRouter } from 'next/navigation';
import { ADMIN_EMAIL } from '../../../lib/config';
import { useAdminLogout } from '../../../hooks/useAdminLogout';
import { useAdminActions } from '../../../hooks/useAdminActions';
import { AdminPasskeyModal } from '../../Admin/AdminPasskeyModal';
import { PointsDisplay, PointsDisplayRef } from '../../Points/PointsDisplay';
import { LoadPointModal } from '../../Admin/LoadPointModal';
import { useIsOwner } from '../../../hooks/useIsOwner';

import classes from './NavBar.module.css';

export function Navbar() {
  const router = useRouter();
  const pathname = usePathname();
  const [active, setActive] = useState(pathname);
  const { data: session } = useSession();
  const email = session?.user?.email || '';

  const [avatarUrl, setAvatarUrl] = useState('');
  const [fullName, setFullName] = useState('');
  const [mounted, setMounted] = useState(false);
  const { setColorScheme } = useMantineColorScheme();
  const computedColorScheme = useComputedColorScheme('light');
  const pointsDisplayRef = useRef<PointsDisplayRef>(null);
  const [loadPointModalOpened, setLoadPointModalOpened] = useState(false);
  const { isOwner } = useIsOwner();
  const { logout } = useAdminLogout();
  const {
    executeAdminAction,
    showPasskeyModal,
    handlePasskeySuccess,
    handlePasskeyClose,
    isAdminUser
  } = useAdminActions();

  const toggleColorScheme = () => {
    setColorScheme(computedColorScheme === 'dark' ? 'light' : 'dark');
  };

  useEffect(() => {
    setMounted(true);
    const storedAvatarUrl = getUserValue(email, 'avatar_url');
    const storedFullName = getUserValue(email, 'full_name');
    if (storedAvatarUrl) {
      setAvatarUrl(storedAvatarUrl);
    }
    if (storedFullName) {
      setFullName(storedFullName);
    }

    const handleStorageChange = () => {
      const newAvatarUrl = getUserValue(email, 'avatar_url');
      const newFullName = getUserValue(email, 'full_name');
      if (newAvatarUrl !== avatarUrl) {
        setAvatarUrl(newAvatarUrl || '');
      }
      if (newFullName !== fullName) {
        setFullName(newFullName || '');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('userDataUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('userDataUpdated', handleStorageChange);
    };
  }, [email]);





  const data = [
    { link: '/', label: 'Home', icon: IconHome },
    { link: '/bookmark', label: 'Bookmarks', icon: IconHeart },
    { link: '/points', label: 'Points', icon: IconCoins },
    { link: '/assets', label: 'Assets', icon: IconTrophy },
  ];

  if (isAdminUser) {
    data.push({ link: '/admin', label: 'Admin Dashboard', icon: IconShield });
  }

  if (isOwner) {
    data.push({ link: '/owner', label: 'Owner Dashboard', icon: IconShield });
  }

  const links = data.map((item) => (
    <a
      className={classes.link}
      data-active={item.link === active || undefined}
      href={item.link}
      key={item.label}
      onClick={(event) => {
        event.preventDefault();
        router.push(item.link);
      }}
    >
      <item.icon className={classes.linkIcon} stroke={1.5} />
      <span>{item.label}</span>
    </a>
  ));

  return (

    <>
      {session ? (
        <nav className={classes.navbar}>
          <div className={classes.navbarMain}>
            <Group className={classes.header} justify="space-between">
              <Group onClick={() => router.push('/')} style={{ cursor: 'pointer' }}>
                {mounted && <Avatar radius="xl" src={avatarUrl} />}
                <div>
                  <Text fw={500}>{fullName}</Text>
                  <Text size="xs" c="dimmed">
                    {email}
                  </Text>
                </div>
              </Group>
            </Group>

            {/* Points Display */}
            <div style={{ padding: '3px 0', borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
              <PointsDisplay
                ref={pointsDisplayRef}
                variant="button"
                size="sm"
                showIcon={true}
                showRefresh={false}
                clickable={true}
              />
            </div>
            {links}
          </div>

          <div className={classes.footer}>
            {isAdminUser && (
              <>
                <a
                  href="#"
                  className={classes.link}
                  onClick={(event) => {
                    event.preventDefault();
                    executeAdminAction(() => setLoadPointModalOpened(true));
                  }}
                >
                  <IconCoins className={classes.linkIcon} stroke={1.5} />
                  <span>Load Point</span>
                </a>
              </>
            )}
            <a
              href="#"
              className={classes.link}
              onClick={(event) => {
                event.preventDefault();
                toggleColorScheme();
              }}
            >
              {computedColorScheme === 'dark' ? (
                <IconSun className={classes.linkIcon} stroke={1.5} />
              ) : (
                <IconMoon className={classes.linkIcon} stroke={1.5} />
              )}
              <span>Toggle Theme</span>
            </a>
            <a
              href="#"
              className={classes.link}
              onClick={(event) => {
                event.preventDefault();
                deleteUserValue(email, 'avatar_url');
                deleteUserValue(email, 'full_name');
                logout();
              }}
            >
              <IconLogout className={classes.linkIcon} stroke={1.5} />
              <span>Logout</span>
            </a>
          </div>

          {/* Load Point Modal for Admin */}
          {isAdminUser && (
            <LoadPointModal
              opened={loadPointModalOpened}
              onClose={() => setLoadPointModalOpened(false)}
            />
          )}

          {/* Admin Passkey Modal */}
          <AdminPasskeyModal
            opened={showPasskeyModal}
            onClose={handlePasskeyClose}
            onSuccess={handlePasskeySuccess}
          />
        </nav>
      ) : (
        <nav className={classes.navbar}>
          <a className={classes.link} href="/">
            <IconHome className={classes.linkIcon} stroke={1.5} />
            <span>Home</span>
          </a>
        </nav>
      )}
    </>

  );
}