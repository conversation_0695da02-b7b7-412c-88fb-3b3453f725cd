import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './database.types';

// Initialize Supabase client with lazy loading pattern
let supabaseInstance: SupabaseClient<Database> | null = null;

// Create a single supabase client for interacting with your database
export function getSupabaseClient(): SupabaseClient<Database> {
  if (supabaseInstance) return supabaseInstance;

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
  }

  supabaseInstance = createClient<Database>(supabaseUrl, supabaseAnonKey);
  return supabaseInstance;
}

// For backward compatibility
export const supabase = getSupabaseClient();

// Define common response type
export type SupabaseResponse<T> = {
  data: T | null;
  error: Error | null;
};

// Define filter operator types
export type FilterOperator = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'is' | 'in' | 'contains';

/**
 * Generic function to fetch data from a table
 * @param table The table name to query
 * @param query Optional query parameters (select, filter, etc.)
 * @returns The query result
 */
export async function fetchData<T>(
  table: string,
  query?: {
    select?: string;
    filter?: { column: string; operator?: FilterOperator; value: any }[];
    order?: { column: string; ascending?: boolean };
    limit?: number;
    single?: boolean;
  }
): Promise<SupabaseResponse<T | T[]>> {
  try {
    const client = getSupabaseClient();
    let queryBuilder = client.from(table).select(query?.select || '*');

    // Apply filters if provided
    if (query?.filter && query.filter.length > 0) {
      query.filter.forEach(({ column, operator = 'eq', value }) => {
        switch (operator) {
          case 'eq':
            queryBuilder = queryBuilder.eq(column, value);
            break;
          case 'neq':
            queryBuilder = queryBuilder.neq(column, value);
            break;
          case 'gt':
            queryBuilder = queryBuilder.gt(column, value);
            break;
          case 'gte':
            queryBuilder = queryBuilder.gte(column, value);
            break;
          case 'lt':
            queryBuilder = queryBuilder.lt(column, value);
            break;
          case 'lte':
            queryBuilder = queryBuilder.lte(column, value);
            break;
          case 'like':
            queryBuilder = queryBuilder.like(column, value);
            break;
          case 'ilike':
            queryBuilder = queryBuilder.ilike(column, value);
            break;
          case 'is':
            queryBuilder = queryBuilder.is(column, value);
            break;
          case 'in':
            queryBuilder = queryBuilder.in(column, value);
            break;
          case 'contains':
            queryBuilder = queryBuilder.contains(column, value);
            break;
          default:
            queryBuilder = queryBuilder.eq(column, value);
        }
      });
    }

    // Apply ordering if provided
    if (query?.order) {
      const { column, ascending = true } = query.order;
      queryBuilder = queryBuilder.order(column, { ascending });
    }

    // Apply limit if provided
    if (query?.limit) {
      queryBuilder = queryBuilder.limit(query.limit);
    }

    // Return single row or all rows
    if (query?.single) {
      const result = await queryBuilder.single<T>();
      return result;
    }

    const result = await queryBuilder;
    return { data: result.data as T[], error: result.error };
  } catch (error) {
    return { data: null, error: error as Error };
  }
}

/**
 * Insert data into a table
 * @param table The table name
 * @param data The data to insert
 * @returns The insert result
 */
export async function insertData<T>(
  table: string,
  data: Record<string, any> | Record<string, any>[]
): Promise<SupabaseResponse<T>> {
  try {
    const client = getSupabaseClient();
    const result = await client.from(table).insert(data).select();
    return { data: result.data as T, error: result.error };
  } catch (error) {
    return { data: null, error: error as Error };
  }
}

/**
 * Update data in a table
 * @param table The table name
 * @param data The data to update
 * @param match The column and value to match for the update
 * @returns The update result
 */
export async function updateData<T>(
  table: string,
  data: Record<string, any>,
  match: { column: string; value: any }
): Promise<SupabaseResponse<T>> {
  try {
    const client = getSupabaseClient();
    const result = await client
      .from(table)
      .update(data)
      .eq(match.column, match.value)
      .select();

    return { data: result.data as T, error: result.error };
  } catch (error) {
    return { data: null, error: error as Error };
  }
}

/**
 * Delete data from a table
 * @param table The table name
 * @param match The column and value to match for deletion
 * @returns The delete result
 */
export async function deleteData(
  table: string,
  match: { column: string; value: any }
): Promise<SupabaseResponse<null>> {
  try {
    const client = getSupabaseClient();
    const result = await client
      .from(table)
      .delete()
      .eq(match.column, match.value);

    return result;
  } catch (error) {
    return { data: null, error: error as Error };
  }
}

/**
 * Upload a file to Supabase storage
 * @param bucket The storage bucket name
 * @param path The file path within the bucket
 * @param file The file to upload
 * @returns The upload result
 */
export async function uploadFile(
  bucket: string,
  path: string,
  file: File
): Promise<SupabaseResponse<{ path: string }>> {
  try {
    const client = getSupabaseClient();
    return await client.storage.from(bucket).upload(path, file, {
      upsert: true,
    });
  } catch (error) {
    return { data: null, error: error as Error };
  }
}

/**
 * Get a public URL for a file in Supabase storage
 * @param bucket The storage bucket name
 * @param path The file path within the bucket
 * @returns The public URL
 */
export function getFileUrl(bucket: string, path: string): string {
  const client = getSupabaseClient();
  return client.storage.from(bucket).getPublicUrl(path).data.publicUrl;
}

/**
 * Delete a file from Supabase storage
 * @param bucket The storage bucket name
 * @param path The file path within the bucket
 * @returns The delete result
 */
export async function deleteFile(
  bucket: string,
  path: string
): Promise<SupabaseResponse<{ path: string }>> {
  try {
    const client = getSupabaseClient();
    const result = await client.storage.from(bucket).remove([path]);
    return {
      data: result.data ? { path } : null,
      error: result.error
    };
  } catch (error) {
    return { data: null, error: error as Error };
  }
}



export async function getDataValue(
  table: string,
  matchColumn: string,
  matchValue: string,
  selectColumn: string
): Promise<any> {
  try {
    const client = getSupabaseClient();
    const { data, error } = await client
      .from(table)
      .select(selectColumn)
      .eq(matchColumn, matchValue);

    if (error) {
      console.error('Error fetching data value:', error);
      return null;
    }

    if (data && data.length === 1) {
      return data[0][selectColumn as keyof typeof data[0]];
    } else if (data && data.length > 1) {
      console.warn('Multiple rows returned, expected single row.');
      return data[0][selectColumn as keyof typeof data[0]]; // Return the first row's value as a fallback
    }

    return null;
  } catch (error) {
    console.error('Unexpected error fetching data value:', error);
    return null;
  }
}