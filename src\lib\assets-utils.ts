/**
 * Asset utility functions for the ODude Assets System
 */

import { Database } from './database.types';

// Type aliases for better readability
export type Asset = Database['public']['Tables']['assets']['Row'];
export type AssetInsert = Database['public']['Tables']['assets']['Insert'];
export type AssetUpdate = Database['public']['Tables']['assets']['Update'];

export type AssetTransfer = Database['public']['Tables']['asset_transfers']['Row'];
export type AssetTransferInsert = Database['public']['Tables']['asset_transfers']['Insert'];
export type AssetTransferUpdate = Database['public']['Tables']['asset_transfers']['Update'];

export type AssetTemplate = Database['public']['Tables']['asset_templates']['Row'];
export type AssetWithStats = Database['public']['Views']['assets_with_stats']['Row'];

// Asset type enum
export const ASSET_TYPES = ['Badge', 'Certificate', 'Ticket', 'Coupon'] as const;
export type AssetType = typeof ASSET_TYPES[number];

// Transfer status enum
export const TRANSFER_STATUSES = ['pending', 'approved', 'declined', 'hidden'] as const;
export type TransferStatus = typeof TRANSFER_STATUSES[number];

// Asset metadata interface
export interface AssetMetadata {
  [key: string]: any;
}

/**
 * Check if an asset is expired
 */
export function isAssetExpired(asset: Asset): boolean {
  if (!asset.expiry_date) return false;
  return new Date(asset.expiry_date) < new Date();
}

/**
 * Check if an asset is active (not deleted and not expired)
 */
export function isAssetActive(asset: Asset): boolean {
  return !asset.is_deleted && !isAssetExpired(asset);
}

/**
 * Get asset type color for UI
 */
export function getAssetTypeColor(assetType: AssetType): string {
  const colors = {
    Badge: '#4F46E5',      // Indigo
    Certificate: '#059669', // Emerald
    Ticket: '#F59E0B',     // Amber
    Coupon: '#EF4444'      // Red
  };
  return colors[assetType];
}

/**
 * Get asset type icon for UI
 */
export function getAssetTypeIcon(assetType: AssetType): string {
  const icons = {
    Badge: 'IconAward',
    Certificate: 'IconCertificate',
    Ticket: 'IconTicket',
    Coupon: 'IconDiscount'
  };
  return icons[assetType];
}

/**
 * Format asset for display
 */
export function formatAssetForDisplay(asset: Asset) {
  return {
    ...asset,
    isExpired: isAssetExpired(asset),
    isActive: isAssetActive(asset),
    typeColor: getAssetTypeColor(asset.asset_type),
    typeIcon: getAssetTypeIcon(asset.asset_type),
    formattedCreatedAt: new Date(asset.created_at).toLocaleDateString(),
    formattedExpiryDate: asset.expiry_date ? new Date(asset.expiry_date).toLocaleDateString() : null
  };
}

/**
 * Validate asset data before creation/update
 */
export function validateAssetData(data: Partial<AssetInsert>): string[] {
  const errors: string[] = [];

  if (!data.title?.trim()) {
    errors.push('Title is required');
  }

  if (!data.asset_type || !ASSET_TYPES.includes(data.asset_type as AssetType)) {
    errors.push('Valid asset type is required');
  }

  if (!data.image_url?.trim()) {
    errors.push('Image is required');
  }

  if (!data.issuer_odude_name?.trim()) {
    errors.push('Issuer ODude name is required');
  }

  if (!data.issuer_email?.trim()) {
    errors.push('Issuer email is required');
  }

  // Validate expiry date if provided
  if (data.expiry_date) {
    const expiryDate = new Date(data.expiry_date);
    if (isNaN(expiryDate.getTime())) {
      errors.push('Invalid expiry date');
    } else if (expiryDate <= new Date()) {
      errors.push('Expiry date must be in the future');
    }
  }

  return errors;
}

/**
 * Validate ODude name format
 */
export function validateODudeName(name: string): boolean {
  if (!name || typeof name !== 'string') return false;
  
  // Basic validation: should contain @ and have valid format
  const parts = name.split('@');
  if (parts.length !== 2) return false;
  
  const [username, domain] = parts;
  if (!username || !domain) return false;
  
  // Additional validation can be added here
  return true;
}

/**
 * Get transfer status color for UI
 */
export function getTransferStatusColor(status: TransferStatus): string {
  const colors = {
    pending: '#F59E0B',    // Amber
    approved: '#10B981',   // Emerald
    declined: '#EF4444',   // Red
    hidden: '#6B7280'      // Gray
  };
  return colors[status];
}

/**
 * Get transfer status label for UI
 */
export function getTransferStatusLabel(status: TransferStatus): string {
  const labels = {
    pending: 'Pending',
    approved: 'Approved',
    declined: 'Declined',
    hidden: 'Hidden'
  };
  return labels[status];
}

/**
 * Format transfer for display
 */
export function formatTransferForDisplay(transfer: AssetTransfer) {
  return {
    ...transfer,
    statusColor: getTransferStatusColor(transfer.status),
    statusLabel: getTransferStatusLabel(transfer.status),
    formattedTransferredAt: new Date(transfer.transferred_at).toLocaleDateString(),
    formattedRespondedAt: transfer.responded_at ? new Date(transfer.responded_at).toLocaleDateString() : null
  };
}

/**
 * Check if user can manage asset (is the issuer)
 */
export function canManageAsset(asset: Asset, userEmail: string): boolean {
  return asset.issuer_email === userEmail;
}

/**
 * Check if user can respond to transfer (is the recipient)
 */
export function canRespondToTransfer(transfer: AssetTransfer, userEmail: string): boolean {
  return transfer.to_email === userEmail && transfer.status === 'pending';
}

/**
 * Get public assets (approved and active)
 */
export function filterPublicAssets(transfers: AssetTransfer[]): AssetTransfer[] {
  return transfers.filter(transfer => 
    transfer.status === 'approved' && 
    // Additional filtering for expired assets would need asset data
    true
  );
}

/**
 * Group transfers by status
 */
export function groupTransfersByStatus(transfers: AssetTransfer[]) {
  return transfers.reduce((groups, transfer) => {
    const status = transfer.status;
    if (!groups[status]) {
      groups[status] = [];
    }
    groups[status].push(transfer);
    return groups;
  }, {} as Record<TransferStatus, AssetTransfer[]>);
}

/**
 * Create default asset metadata
 */
export function createDefaultAssetMetadata(assetType: AssetType): AssetMetadata {
  const defaults = {
    Badge: { level: 'Bronze', category: 'Achievement' },
    Certificate: { institution: '', course: '', grade: '' },
    Ticket: { event: '', venue: '', seat: '' },
    Coupon: { discount: '', validUntil: '', terms: '' }
  };
  return defaults[assetType] || {};
}

/**
 * Generate asset filename for uploads
 */
export function generateAssetFilename(title: string, assetType: AssetType): string {
  const sanitizedTitle = title.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const timestamp = Date.now();
  return `${assetType.toLowerCase()}-${sanitizedTitle}-${timestamp}`;
}
