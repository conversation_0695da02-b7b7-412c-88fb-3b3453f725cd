import { NextRequest, NextResponse } from 'next/server';
import { fetchData, getSupabaseClient } from 'src/lib/supabase';
import { getSocialData, getCryptoData } from 'src/lib/database-utils';

type RouteParams = {
  params: Promise<{ name: string }>
};

export async function GET(request: NextRequest, { params }: RouteParams) {
  const { name } = await params;
  
  if (!name) {
    return NextResponse.json({ error: 'Name parameter is required' }, { status: 400 });
  }

  try {
    // Fetch contact data from the database using lowercase name
    const client = getSupabaseClient();
    const { data, error } = await client
      .from('contact')
      .select('*')
      .eq('name', name.toLowerCase())
      .single();
    
    if (error) {
      console.error('Error fetching contact data:', error);
      return NextResponse.json({ error: 'Failed to fetch contact data' }, { status: 500 });
    }
    
    if (!data) {
      return NextResponse.json({ error: `Contact not found: ${name}` }, { status: 404 });
    }

    // Format the response according to the required structure
    const response = {
      name: data.name,
      description: data.description || "ODude Name service",
      image: data.image || `https://storage.googleapis.com/odudename.appspot.com/${data.name}.jpg`,
      attributes: [
        { trait_type: "domain", value: data.name },
        { trait_type: "level", value: 2 },
        { trait_type: "length", value: data.name.length }
      ],
      records: {
        "1": { type: "name", value: data.profile || data.name },
        "2": { type: "email", value: data.email || "" },
        "3": { type: "website", value: data.website || "" },
        "4": { type: "phone", value: data.phone || "" },
        "5": { type: "tg_bot", value: data.tg_bot || "" },
        "6": {
          type: "social",
          value: getSocialData(data)
        },
        "7": {
          type: "link",
          value: data.links || { "": "" }
        },
        "8": {
          type: "crypto",
          value: getCryptoData(data)
        },
        "9": { type: "notes", value: data.notes || "" },
        "20": {
          type: "img",
          value: data.images || {
            img1: "",
            img2: "",
            img3: ""
          }
        },
        "50": { type: "web_url", value: data.web2 || "" },
        "51": { type: "web3_url", value: data.web3 || "" }
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}