// Admin session key for localStorage
const ADMIN_SESSION_KEY = 'odude_admin_session';
const ADMIN_SESSION_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export interface AdminSession {
  authenticated: boolean;
  timestamp: number;
  expiresAt: number;
}

/**
 * Verify admin passkey via server-side API
 */
export async function verifyAdminPasskey(passkey: string): Promise<boolean> {
  try {
    const response = await fetch('/api/admin/verify-passkey', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ passkey }),
    });

    if (response.ok) {
      const data = await response.json();
      return data.valid === true;
    }

    return false;
  } catch (error) {
    console.error('Error verifying passkey:', error);
    return false;
  }
}

/**
 * Create admin session
 */
export function createAdminSession(): void {
  const now = Date.now();
  const session: AdminSession = {
    authenticated: true,
    timestamp: now,
    expiresAt: now + ADMIN_SESSION_EXPIRY
  };
  
  if (typeof window !== 'undefined') {
    localStorage.setItem(ADMIN_SESSION_KEY, JSON.stringify(session));
  }
}

/**
 * Get current admin session
 */
export function getAdminSession(): AdminSession | null {
  if (typeof window === 'undefined') {
    return null;
  }
  
  try {
    const sessionData = localStorage.getItem(ADMIN_SESSION_KEY);
    if (!sessionData) {
      return null;
    }
    
    const session: AdminSession = JSON.parse(sessionData);
    
    // Check if session is expired
    if (Date.now() > session.expiresAt) {
      clearAdminSession();
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Error parsing admin session:', error);
    clearAdminSession();
    return null;
  }
}

/**
 * Check if admin session is valid
 */
export function isAdminSessionValid(): boolean {
  const session = getAdminSession();
  return session !== null && session.authenticated;
}

/**
 * Clear admin session
 */
export function clearAdminSession(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(ADMIN_SESSION_KEY);
  }
}

/**
 * Extend admin session (refresh expiry)
 */
export function extendAdminSession(): void {
  const session = getAdminSession();
  if (session) {
    session.expiresAt = Date.now() + ADMIN_SESSION_EXPIRY;
    if (typeof window !== 'undefined') {
      localStorage.setItem(ADMIN_SESSION_KEY, JSON.stringify(session));
    }
  }
}

/**
 * Initialize admin session listener for logout events
 * This should be called in the app initialization
 */
export function initializeAdminSessionListener(): void {
  if (typeof window !== 'undefined') {
    // Listen for storage events to sync admin session across tabs
    window.addEventListener('storage', (e) => {
      if (e.key === ADMIN_SESSION_KEY && e.newValue === null) {
        // Admin session was cleared in another tab
        window.location.reload();
      }
    });

    // Listen for beforeunload to extend session if user is active
    window.addEventListener('beforeunload', () => {
      if (isAdminSessionValid()) {
        extendAdminSession();
      }
    });
  }
}
