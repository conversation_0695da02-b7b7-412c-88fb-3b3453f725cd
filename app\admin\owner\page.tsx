'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  Paper,
  Group,
  ThemeIcon,
  Stack,
  Button,
  Table,
  ActionIcon,
  Modal,
  TextInput,
  Select,
  ScrollArea,
  Radio,
  Badge,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconShield,
  IconPlus,
  IconEdit,
  IconTrash,
  IconUsers,
} from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { checkAdminAccess, AdminAccessInfo } from 'src/lib/adminClient';
import { NAME_SLOTS } from 'src/lib/config';

interface PrimaryNameOwner {
  id: number;
  user_email: string;
  owner_of: string;
  ownership_type: 'static' | 'dynamic';
  created_at: string;
  updated_at: string;
}

export default function OwnerManagementPage() {
  const [owners, setOwners] = useState<PrimaryNameOwner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);
  const [modalOpened, setModalOpened] = useState(false);
  const [editingOwner, setEditingOwner] = useState<PrimaryNameOwner | null>(null);
  const [formData, setFormData] = useState({
    user_email: '',
    owner_of: '',
    ownership_type: 'static' as 'static' | 'dynamic',
  });

  useEffect(() => {
    // Check admin access
    const checkAccess = async () => {
      try {
        const accessInfo = await checkAdminAccess();
        setAdminAccess(accessInfo);

        if (!accessInfo.isAuthorized || !accessInfo.isSuperAdmin) {
          setError('Super admin privileges required');
          return;
        }

        await fetchOwners();
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to check access');
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, []);

  const fetchOwners = async () => {
    try {
      const response = await fetch('/api/admin/owner');
      if (!response.ok) {
        throw new Error('Failed to fetch owners');
      }
      const data = await response.json();
      setOwners(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch owners');
    }
  };

  const handleAddOwner = () => {
    setEditingOwner(null);
    setFormData({ user_email: '', owner_of: '', ownership_type: 'static' });
    setModalOpened(true);
  };

  const handleEditOwner = (owner: PrimaryNameOwner) => {
    setEditingOwner(owner);
    setFormData({
      user_email: owner.user_email,
      owner_of: owner.owner_of,
      ownership_type: owner.ownership_type || 'static',
    });
    setModalOpened(true);
  };

  const handleDeleteOwner = (owner: PrimaryNameOwner) => {
    modals.openConfirmModal({
      title: 'Delete Owner Assignment',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to remove ownership of "{owner.owner_of}" from "{owner.user_email}"?
          This will revoke their admin access to this namespace.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const response = await fetch('/api/admin/owner', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id: owner.id }),
          });

          if (!response.ok) {
            throw new Error('Failed to delete owner');
          }

          notifications.show({
            title: 'Success',
            message: 'Owner assignment deleted successfully',
            color: 'green',
          });

          await fetchOwners();
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to delete owner',
            color: 'red',
          });
        }
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const method = editingOwner ? 'PUT' : 'POST';
      const body = editingOwner 
        ? { ...formData, id: editingOwner.id }
        : formData;

      const response = await fetch('/api/admin/owner', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save owner');
      }

      notifications.show({
        title: 'Success',
        message: `Owner ${editingOwner ? 'updated' : 'added'} successfully`,
        color: 'green',
      });

      setModalOpened(false);
      await fetchOwners();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to save owner',
        color: 'red',
      });
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  const rows = owners.map((owner) => (
    <Table.Tr key={owner.id}>
      <Table.Td>{owner.user_email}</Table.Td>
      <Table.Td>{owner.owner_of}</Table.Td>
      <Table.Td>
        <Badge color={owner.ownership_type === 'static' ? 'blue' : 'green'} variant="light">
          {owner.ownership_type === 'static' ? 'Static' : 'Dynamic'}
        </Badge>
      </Table.Td>
      <Table.Td>{new Date(owner.created_at).toLocaleDateString()}</Table.Td>
      <Table.Td>
        <Group gap="xs">
          <ActionIcon
            color="blue"
            variant="light"
            onClick={() => handleEditOwner(owner)}
            title="Edit Owner"
          >
            <IconEdit size={16} />
          </ActionIcon>
          <ActionIcon
            color="red"
            variant="light"
            onClick={() => handleDeleteOwner(owner)}
            title="Delete Owner"
          >
            <IconTrash size={16} />
          </ActionIcon>
        </Group>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="blue">
              <IconUsers size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Primary Name Owners</Title>
              <Text c="dimmed">Manage namespace ownership assignments</Text>
            </div>
          </Group>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleAddOwner}
          >
            Add Owner
          </Button>
        </Group>

        {/* Owners Table */}
        <Paper withBorder>
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Email</Table.Th>
                  <Table.Th>Primary Name</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Created</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {rows.length > 0 ? (
                  rows
                ) : (
                  <Table.Tr>
                    <Table.Td colSpan={5}>
                      <Text ta="center" c="dimmed">
                        No owners assigned yet
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </ScrollArea>
        </Paper>

        {/* Add/Edit Modal */}
        <Modal
          opened={modalOpened}
          onClose={() => setModalOpened(false)}
          title={editingOwner ? 'Edit Owner' : 'Add New Owner'}
          centered
        >
          <Stack gap="md">
            <TextInput
              label="User Email"
              placeholder="<EMAIL>"
              value={formData.user_email}
              onChange={(e) => setFormData({ ...formData, user_email: e.target.value })}
              required
            />
            <TextInput
              label="Primary Name"
              placeholder="Enter primary name (e.g., shop, store, etc.)"
              value={formData.owner_of}
              onChange={(e) => setFormData({ ...formData, owner_of: e.target.value })}
              required
            />
            <div>
              <Text size="sm" fw={500} mb="xs">
                Ownership Type
              </Text>
              <Radio.Group
                value={formData.ownership_type}
                onChange={(value) => setFormData({ ...formData, ownership_type: value as 'static' | 'dynamic' })}
              >
                <Stack gap="xs">
                  <Radio value="static" label="Static - Contacts cannot be transferred or deleted by holders" />
                  <Radio value="dynamic" label="Dynamic - Contacts can be transferred and deleted as normal" />
                </Stack>
              </Radio.Group>
            </div>
            <Group justify="flex-end" mt="md">
              <Button variant="light" onClick={() => setModalOpened(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmit}>
                {editingOwner ? 'Update' : 'Add'} Owner
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </AdminLayout>
  );
}
