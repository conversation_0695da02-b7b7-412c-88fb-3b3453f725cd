import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { checkAdminAccess, AdminAccessInfo } from '../lib/adminClient';
import { checkOwnerAccess, OwnerAccessInfo } from '../lib/ownerClient';

/**
 * Hook to check if the current user is an owner of any primary names
 * Returns owner status and owned primary names
 * This does NOT require admin session verification
 */
export function useIsOwner() {
  const { data: session } = useSession();
  const [isOwner, setIsOwner] = useState(false);
  const [ownedPrimaryNames, setOwnedPrimaryNames] = useState<string[]>([]);
  const [ownedPrimaryNamesWithTypes, setOwnedPrimaryNamesWithTypes] = useState<{name: string, type: 'static' | 'dynamic'}[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkOwnerStatus = async () => {
      if (!session?.user?.email) {
        setIsOwner(false);
        setOwnedPrimaryNames([]);
        setOwnedPrimaryNamesWithTypes([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const accessInfo = await checkOwnerAccess();
        setIsOwner(accessInfo.isOwner);
        setOwnedPrimaryNames(accessInfo.ownedPrimaryNames);
        setOwnedPrimaryNamesWithTypes(accessInfo.ownedPrimaryNamesWithTypes);
        setError(null);
      } catch (err) {
        console.error('Error checking owner status:', err);
        setIsOwner(false);
        setOwnedPrimaryNames([]);
        setOwnedPrimaryNamesWithTypes([]);
        setError(err instanceof Error ? err.message : 'Failed to check owner status');
      } finally {
        setLoading(false);
      }
    };

    checkOwnerStatus();
  }, [session?.user?.email]);

  return {
    isOwner,
    ownedPrimaryNames,
    ownedPrimaryNamesWithTypes,
    loading,
    error,
  };
}

/**
 * Hook to get full admin access information including owner status
 * This is useful when you need both admin and owner information
 */
export function useAdminAccess() {
  const { data: session } = useSession();
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAccess = async () => {
      if (!session?.user?.email) {
        setAdminAccess(null);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const accessInfo = await checkAdminAccess();
        setAdminAccess(accessInfo);
        setError(null);
      } catch (err) {
        console.error('Error checking admin access:', err);
        setAdminAccess(null);
        setError(err instanceof Error ? err.message : 'Failed to check admin access');
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [session?.user?.email]);

  return {
    adminAccess,
    loading,
    error,
    // Convenience properties
    isOwner: adminAccess?.isOwner || false,
    isSuperAdmin: adminAccess?.isSuperAdmin || false,
    isAuthorized: adminAccess?.isAuthorized || false,
    ownedPrimaryNames: adminAccess?.ownedPrimaryNames || [],
  };
}
