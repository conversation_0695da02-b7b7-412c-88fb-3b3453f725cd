import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const supabase = getSupabaseClient();

    // Fetch the last 50 contacts ordered by timestamp (most recent first)
    const { data, error } = await supabase
      .from('contact')
      .select('name, timestamp, image, profile')
      .order('timestamp', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching recent contacts:', error);
      return NextResponse.json({ error: 'Failed to fetch recent contacts' }, { status: 500 });
    }

    // Format the response
    const formattedContacts = data?.map(contact => ({
      name: contact.name,
      date: contact.timestamp,
      image: contact.image,
      profile: contact.profile
    })) || [];

    const response = NextResponse.json({
      success: true,
      count: formattedContacts.length,
      contacts: formattedContacts
    });

    // Add CORS headers for public API access
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    return response;

  } catch (error) {
    console.error('Recent contacts API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Add CORS headers for public API access
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
