import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dates/styles.css';
import { MantineProvider } from 'src/providers/MantineProvider';
import type { Metadata } from 'next';
import React from 'react';
import { SessionProvider } from "next-auth/react";
import { auth } from "auth";
import { FooterProvider } from "src/providers/FooterProvider";
import AdsScriptLoader from 'src/components/AdSense/AdsScriptLoader';
import { AdminSessionInitializer } from 'src/components/Admin/AdminSessionInitializer';

export const metadata: Metadata = {
  title: 'ODude Desktop',
  description: 'Next.js app using Mantine AppShell',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <html lang="en">
      <head>
        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-ZTB71YNGC8"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-ZTB71YNGC8');
            `,
          }}
        />
        {/* Removed AdSense <Script> as per best practice */}
      </head>
      <body>
        <AdsScriptLoader />
        <SessionProvider session={session}>
          <MantineProvider>
            <FooterProvider>
              <AdminSessionInitializer />
              {children}
            </FooterProvider>
          </MantineProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
