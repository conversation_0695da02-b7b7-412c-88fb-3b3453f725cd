'use client';

import { useState } from 'react';
import {
  Modal,
  TextInput,
  Button,
  Stack,
  Text,
  Alert,
  Group,
  ThemeIcon,
  PasswordInput,
} from '@mantine/core';
import { IconShield, IconAlertCircle } from '@tabler/icons-react';
import { signOut } from 'next-auth/react';
import { verifyAdminPasskey, createAdminSession } from 'src/lib/adminSession';

interface AdminPasskeyModalProps {
  opened: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AdminPasskeyModal({ opened, onClose, onSuccess }: AdminPasskeyModalProps) {
  const [passkey, setPasskey] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Verify passkey via server-side API
      const isValid = await verifyAdminPasskey(passkey);

      if (!isValid) {
        setError('Invalid passkey. You will be logged out for security.');

        // Wait a moment to show the error, then logout
        setTimeout(async () => {
          await signOut({ callbackUrl: '/' });
        }, 2000);

        setLoading(false);
        return;
      }

      // Create admin session
      createAdminSession();

      // Success
      setPasskey('');
      onSuccess();
      onClose();
    } catch (err) {
      console.error('Passkey verification error:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setPasskey('');
    setError(null);
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={
        <Group>
          <ThemeIcon color="red" variant="light" size="lg">
            <IconShield size={20} />
          </ThemeIcon>
          <Text fw={600} size="lg">Admin Security Verification</Text>
        </Group>
      }
      centered
      closeOnClickOutside={false}
      closeOnEscape={false}
      withCloseButton={false}
      size="md"
      zIndex={1000}
    >
      <form onSubmit={handleSubmit}>
        <Stack gap="md">
          <Text size="sm" c="dimmed">
            This area requires additional security verification. Please enter the admin passkey to continue.
          </Text>

          {error && (
            <Alert
              icon={<IconAlertCircle size={16} />}
              color="red"
              variant="light"
            >
              {error}
            </Alert>
          )}

          <PasswordInput
            label="Admin Passkey"
            placeholder="Enter admin passkey"
            value={passkey}
            onChange={(e) => setPasskey(e.currentTarget.value)}
            required
            autoFocus
            disabled={loading}
            data-autofocus
          />

          <Group justify="flex-end" mt="md">
            <Button
              type="submit"
              loading={loading}
              disabled={!passkey.trim()}
              color="red"
            >
              Verify Access
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
