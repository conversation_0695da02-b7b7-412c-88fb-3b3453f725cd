import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const name = searchParams.get('name');

  if (!name) {
    return NextResponse.json({ error: 'Name parameter is required' }, { status: 400 });
  }

  try {
    const response = await fetch(`https://odude-resolve.vercel.app/api/?name=${name}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    // Return the same format as the original API
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching NFT owner:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch NFT owner information',
      address: null,
      code: 500
    }, { status: 500 });
  }
}
