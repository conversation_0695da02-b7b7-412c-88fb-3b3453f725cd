/**
 * Global ownership utility functions
 * Used across the application to check ownership types and ownership status
 */

import { getSupabaseClient } from './supabase';

// Cache for ownership data to avoid repeated API calls
const ownershipCache = new Map<string, { type: 'static' | 'dynamic'; timestamp: number }>();
const ownerCache = new Map<string, { isOwner: boolean; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Extract the Top Level Name (TLN) from a contact name
 * @param contactName - Full contact name (e.g., "cozy@store")
 * @returns The TLN part (e.g., "store") or null if invalid format
 */
export function extractTLN(contactName: string): string | null {
  if (!contactName || typeof contactName !== 'string') return null;
  
  const parts = contactName.split('@');
  if (parts.length !== 2) return null;
  
  const [, tln] = parts;
  return tln || null;
}

/**
 * Check if a contact name belongs to a static TLN
 * @param contactName - Full contact name (e.g., "cozy@store")
 * @returns Promise<boolean> - true if the TLN is static, false if dynamic or not found
 */
export async function isStatic(contactName: string): Promise<boolean> {
  const tln = extractTLN(contactName);
  if (!tln) return false;

  // Import NAME_SLOTS to check if TLN is dynamic
  const { NAME_SLOTS } = await import('./config');

  // Check if TLN is in NAME_SLOTS first - these are always dynamic
  if (NAME_SLOTS.includes(tln)) {
    return false; // Dynamic, so not static
  }

  // Check cache first
  const cacheKey = `tln_${tln}`;
  const cached = ownershipCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.type === 'static';
  }

  try {
    const supabase = getSupabaseClient();

    const { data: owner, error } = await supabase
      .from('primary_name_owners')
      .select('ownership_type')
      .eq('owner_of', tln)
      .single();

    if (error || !owner) {
      // If no ownership record found, default to static for safety
      ownershipCache.set(cacheKey, { type: 'static', timestamp: Date.now() });
      return true;
    }

    const ownershipType = owner.ownership_type || 'static';
    ownershipCache.set(cacheKey, { type: ownershipType, timestamp: Date.now() });

    return ownershipType === 'static';
  } catch (error) {
    console.error('Error checking ownership type:', error);
    // Default to static for safety
    return true;
  }
}

/**
 * Check if a contact name belongs to a dynamic TLN
 * @param contactName - Full contact name (e.g., "cozy@store")
 * @returns Promise<boolean> - true if the TLN is dynamic, false if static or not found
 */
export async function isDynamic(contactName: string): Promise<boolean> {
  return !(await isStatic(contactName));
}

/**
 * Check if an email is the owner of a specific contact name
 * @param email - Email address to check
 * @param contactName - Full contact name (e.g., "cozy@store")
 * @returns Promise<boolean> - true if the email owns the TLN of the contact name
 */
export async function isOwnerOf(email: string, contactName: string): Promise<boolean> {
  const tln = extractTLN(contactName);
  if (!tln || !email) return false;

  // Check cache first
  const cacheKey = `owner_${email}_${tln}`;
  const cached = ownerCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.isOwner;
  }

  try {
    const supabase = getSupabaseClient();
    
    const { data: owner, error } = await supabase
      .from('primary_name_owners')
      .select('id')
      .eq('user_email', email.toLowerCase())
      .eq('owner_of', tln)
      .single();

    const isOwner = !error && !!owner;
    ownerCache.set(cacheKey, { isOwner, timestamp: Date.now() });
    
    return isOwner;
  } catch (error) {
    console.error('Error checking ownership:', error);
    return false;
  }
}

/**
 * Get the ownership type of a TLN
 * @param tln - Top Level Name (e.g., "store")
 * @returns Promise<'static' | 'dynamic' | null> - ownership type or null if not found
 */
export async function getOwnershipType(tln: string): Promise<'static' | 'dynamic' | null> {
  if (!tln) return null;

  // Check cache first
  const cacheKey = `tln_${tln}`;
  const cached = ownershipCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.type;
  }

  try {
    const supabase = getSupabaseClient();
    
    const { data: owner, error } = await supabase
      .from('primary_name_owners')
      .select('ownership_type')
      .eq('owner_of', tln)
      .single();

    if (error || !owner) {
      return null;
    }

    const ownershipType = owner.ownership_type || 'static';
    ownershipCache.set(cacheKey, { type: ownershipType, timestamp: Date.now() });
    
    return ownershipType;
  } catch (error) {
    console.error('Error getting ownership type:', error);
    return null;
  }
}

/**
 * Clear the ownership cache (useful for testing or when ownership changes)
 */
export function clearOwnershipCache(): void {
  ownershipCache.clear();
  ownerCache.clear();
}

/**
 * Check if a user can perform actions (disable/delete) on a contact
 * @param userEmail - Email of the user trying to perform the action
 * @param contactName - Full contact name (e.g., "cozy@store")
 * @param isSuperAdmin - Whether the user is a super admin
 * @returns Promise<boolean> - true if the user can perform actions
 */
export async function canPerformContactActions(
  userEmail: string, 
  contactName: string, 
  isSuperAdmin: boolean = false
): Promise<boolean> {
  // Super admin can always perform actions
  if (isSuperAdmin) return true;

  // Check if the contact belongs to a static TLN
  const isStaticTLN = await isStatic(contactName);
  if (isStaticTLN) return false;

  // For dynamic TLNs, check if user is the owner
  return await isOwnerOf(userEmail, contactName);
}
