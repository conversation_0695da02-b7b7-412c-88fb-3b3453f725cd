# Supabase Integration

This directory contains the necessary files for integrating Supabase with the Next.js application.

## Files

- `supabase.ts` - Main configuration file with utility functions for database operations
- `database.types.ts` - TypeScript definitions for the database schema

## Setup

1. Create a Supabase project at [https://supabase.com](https://supabase.com)
2. Copy your project URL and anon key from the Supabase dashboard
3. Add these values to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## Usage

### Basic Database Operations

```typescript
import { fetchData, insertData, updateData, deleteData } from '@/lib/supabase';

// Fetch data
const { data, error } = await fetchData('table_name', {
  select: 'column1, column2',
  filter: [{ column: 'id', value: 123 }],
  order: { column: 'created_at', ascending: false },
  limit: 10
});

// Insert data
const { data, error } = await insertData('table_name', { 
  column1: 'value1', 
  column2: 'value2' 
});

// Update data
const { data, error } = await updateData('table_name', 
  { column1: 'new_value' },
  { column: 'id', value: 123 }
);

// Delete data
const { data, error } = await deleteData('table_name', 
  { column: 'id', value: 123 }
);
```

### File Storage

```typescript
import { uploadFile, getFileUrl, deleteFile } from '@/lib/supabase';

// Upload a file
const { data, error } = await uploadFile('bucket_name', 'file_path.jpg', file);

// Get a public URL
const url = getFileUrl('bucket_name', 'file_path.jpg');

// Delete a file
const { data, error } = await deleteFile('bucket_name', 'file_path.jpg');
```

## Type Safety

The `database.types.ts` file provides TypeScript definitions for your database schema. You can update this file to match your actual database schema for better type safety.

You can also generate types automatically from your Supabase database using the Supabase CLI:

```bash
npx supabase gen types typescript --project-id your-project-id --schema public > src/lib/database.types.ts
```