// app/profile/[id]/layout.tsx
// Dynamic metadata for profile pages

import type { Metadata } from 'next';
import { ReactNode } from 'react';
import { getSupabaseClient } from 'src/lib/supabase';
import { VIEW_PROFILE_URL, DEFAULT_AVATAR_URL } from 'src/lib/config';

export async function generateMetadata(
  { params }: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  const { id } = await params;
  const contactName = decodeURIComponent(id).toLowerCase();

  // Default fallback values
  const defaultTitle = `ODude Profile - ${contactName}`;
  const defaultDescription = `View ${contactName}'s public profile and links on ODude.`;
  const defaultImage = DEFAULT_AVATAR_URL;
  const profileUrl = `${VIEW_PROFILE_URL}${contactName}`;

  try {
    const client = getSupabaseClient();
    const { data } = await client
      .from('contact')
      .select('profile,name,description,image')
      .eq('name', contactName)
      .single();

    const title = data?.profile ? `${data.profile} - ODude Profile` : defaultTitle;
    const description = data?.description || defaultDescription;
    const image = data?.image || defaultImage;

    return {
      title,
      description,
      // Open Graph tags
      openGraph: {
        title,
        description,
        url: profileUrl,
        siteName: 'ODude',
        images: [
          {
            url: image,
            width: 500,
            height: 500,
            alt: `${data?.profile || contactName}'s profile picture`,
          },
        ],
        locale: 'en_US',
        type: 'profile',
      },
      // Twitter Card tags
      twitter: {
        card: 'summary',
        title,
        description,
        images: [image],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags
      other: {
        'profile:first_name': data?.profile?.split(' ')[0] || contactName,
        'profile:last_name': data?.profile?.split(' ').slice(1).join(' ') || '',
        'profile:username': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: profileUrl,
      },
    };
  } catch {
    return {
      title: defaultTitle,
      description: defaultDescription,
      // Open Graph tags (fallback)
      openGraph: {
        title: defaultTitle,
        description: defaultDescription,
        url: profileUrl,
        siteName: 'ODude',
        images: [
          {
            url: defaultImage,
            width: 500,
            height: 500,
            alt: `${contactName}'s profile picture`,
          },
        ],
        locale: 'en_US',
        type: 'profile',
      },
      // Twitter Card tags (fallback)
      twitter: {
        card: 'summary',
        title: defaultTitle,
        description: defaultDescription,
        images: [defaultImage],
        creator: '@ODude',
        site: '@ODude',
      },
      // Additional meta tags (fallback)
      other: {
        'profile:first_name': contactName,
        'profile:last_name': '',
        'profile:username': contactName,
      },
      // Canonical URL
      alternates: {
        canonical: profileUrl,
      },
    };
  }
}

// DO NOT include `params` here
export default function Layout({ children }: { children: ReactNode }) {
  return <>{children}</>;
}
