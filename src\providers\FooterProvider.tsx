'use client';

import { createContext, useContext, useState, ReactNode, FC, PropsWithChildren, useCallback, useMemo } from 'react';

interface FooterContextType {
  footerContent: ReactNode;
  setFooterContent: (content: ReactNode) => void;
  clearFooterContent: () => void;
}

const FooterContext = createContext<FooterContextType | undefined>(undefined);

export const useFooter = () => {
  const context = useContext(FooterContext);
  if (context === undefined) {
    throw new Error('useFooter must be used within a FooterProvider');
  }
  return context;
};

export const FooterProvider: FC<PropsWithChildren> = ({ children }) => {
  const [footerContent, setFooterContent] = useState<ReactNode>(null);

  const clearFooterContent = useCallback(() => {
    setFooterContent(null);
  }, []);

  const contextValue = useMemo(() => ({
    footerContent,
    setFooterContent,
    clearFooterContent
  }), [footerContent, clearFooterContent]);

  return (
    <FooterContext.Provider value={contextValue}>
      {children}
    </FooterContext.Provider>
  );
};
