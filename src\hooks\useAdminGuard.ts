'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { isAdmin } from 'src/lib/admin';
import { isAdminSessionValid, extendAdminSession } from 'src/lib/adminSession';

export interface UseAdminGuardReturn {
  isLoading: boolean;
  isAuthorized: boolean;
  needsPasskey: boolean;
  showPasskeyModal: boolean;
  setShowPasskeyModal: (show: boolean) => void;
  handlePasskeySuccess: () => void;
}

export function useAdminGuard(): UseAdminGuardReturn {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [needsPasskey, setNeedsPasskey] = useState(false);
  const [showPasskeyModal, setShowPasskeyModal] = useState(false);

  useEffect(() => {
    if (status === 'loading') {
      return;
    }

    // Check if user is authenticated
    if (!session?.user?.email) {
      router.push('/');
      return;
    }

    // Check if user is admin
    if (!isAdmin(session.user.email)) {
      router.push('/');
      return;
    }

    // User is admin, now check if they have valid admin session
    const hasValidAdminSession = isAdminSessionValid();
    
    if (hasValidAdminSession) {
      // Extend the session since user is actively using admin features
      extendAdminSession();
      setIsAuthorized(true);
      setNeedsPasskey(false);
      setIsLoading(false);
    } else {
      // Need passkey verification
      setIsAuthorized(false);
      setNeedsPasskey(true);
      setShowPasskeyModal(true);
      setIsLoading(false);
    }
  }, [session, status, router]);

  const handlePasskeySuccess = () => {
    setIsAuthorized(true);
    setNeedsPasskey(false);
    setShowPasskeyModal(false);
  };

  return {
    isLoading,
    isAuthorized,
    needsPasskey,
    showPasskeyModal,
    setShowPasskeyModal,
    handlePasskeySuccess,
  };
}
