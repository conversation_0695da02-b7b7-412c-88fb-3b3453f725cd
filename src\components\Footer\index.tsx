'use client';

import { Flex, Anchor, Text, Divider, Group, Stack } from "@mantine/core";
import { useFooter } from "src/providers/FooterProvider";
import Link from "next/link";

export const Footer = () => {
  const { footerContent } = useFooter();

  // Always render footer with links, but show dynamic content if available
  return (
    <Stack gap="xs" align="center">
      {footerContent ? (
        <>
          {footerContent}
          <Divider size="xs" w="100%" />
        </>
      ) : null}

      <Group justify="center">
        <Anchor component={Link} href="https://odude.com/" size="sm" c="dimmed" target="_blank">
          ODude.com
        </Anchor>
        <Text c="dimmed" size="sm">|</Text>
        <Anchor component={Link} href="/privacy-policy" size="sm" c="dimmed">
          Privacy Policy
        </Anchor>
        <Text c="dimmed" size="sm">|</Text>
        <Anchor component={Link} href="/contact-us" size="sm" c="dimmed">
          Contact Us
        </Anchor>
        <Text c="dimmed" size="sm">|</Text>
        <Anchor component={Link} href="/report" size="sm" c="dimmed">
          Report Profile
        </Anchor>
      </Group>
    </Stack>
  );
};
