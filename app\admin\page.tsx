'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Tabs,
  Stack,
  ActionIcon,
  Button,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconAddressBook,
  IconBookmark,
  IconShield,
  IconDatabase,
  IconTrash,
  IconCoins,
  IconTemplate,
} from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { AdminStats, fetchAdminStats } from 'src/lib/admin';
import { ProfilesTable } from 'src/components/Admin/ProfilesTable';
import { ContactsTable } from 'src/components/Admin/ContactsTable';
import { AssetTemplatesTable } from 'src/components/Admin/AssetTemplatesTable';
import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { checkAdminAccess, AdminAccessInfo } from 'src/lib/adminClient';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}



function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}



export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);



  useEffect(() => {
    // Check admin access and fetch stats
    const fetchData = async () => {
      try {
        // First check admin access
        const accessInfo = await checkAdminAccess();
        setAdminAccess(accessInfo);

        if (!accessInfo.isAuthorized) {
          setError(accessInfo.error || 'Unauthorized access');
          return;
        }

        // Then fetch stats
        const statsData = await fetchAdminStats();
        setStats(statsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  // Only allow super admin access to /admin
  if (adminAccess && !adminAccess.isSuperAdmin) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Access Denied" color="red">
          Super admin privileges required. Use /owner for namespace management.
        </Alert>
      </AdminLayout>
    );
  }

  // Show super admin dashboard
  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="red">
              <IconShield size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Admin Dashboard</Title>
              <Text c="dimmed">Manage users, contacts, and system statistics</Text>
            </div>
          </Group>
          <Group>
            <Button
              variant="light"
              leftSection={<IconUsers size={16} />}
              onClick={() => window.location.href = '/admin/owner'}
            >
              Manage Owners
            </Button>
            <Button
              variant="light"
              leftSection={<IconCoins size={16} />}
              onClick={() => window.location.href = '/admin/points'}
            >
              Points Log
            </Button>
            <Button
              variant="light"
              leftSection={<IconDatabase size={16} />}
              onClick={() => window.location.href = '/admin/system-info'}
            >
              System Info
            </Button>
          </Group>
        </Group>

        {/* Statistics Cards */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
            <StatCard
              title="Total Profiles"
              value={stats.profiles}
              icon={<IconUsers size={18} />}
              color="blue"
            />
            <StatCard
              title="Total Contacts"
              value={stats.contacts}
              icon={<IconAddressBook size={18} />}
              color="green"
            />
            <StatCard
              title="Total Bookmarks"
              value={stats.bookmarks}
              icon={<IconBookmark size={18} />}
              color="orange"
            />
            <StatCard
              title="Asset Templates"
              value={stats.assetTemplates || 0}
              icon={<IconTemplate size={18} />}
              color="purple"
            />
          </SimpleGrid>
        )}

        {/* Quick Actions */}
        {adminAccess?.isSuperAdmin && (
          <Paper withBorder p="md">
            <Group justify="space-between" align="center">
              <div>
                <Text fw={500} size="sm">Database Management</Text>
                <Text size="xs" c="dimmed">
                  Advanced database backup and maintenance tools
                </Text>
              </div>
              <Button
                leftSection={<IconDatabase size={16} />}
                onClick={() => window.location.href = '/admin/backup'}
                variant="light"
                color="blue"
              >
                Backup Management
              </Button>
            </Group>
          </Paper>
        )}

        {/* Data Tables */}
        <Tabs defaultValue="profiles" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
              Profiles
            </Tabs.Tab>
            <Tabs.Tab value="contacts" leftSection={<IconAddressBook size={16} />}>
              Contacts
            </Tabs.Tab>
            <Tabs.Tab value="templates" leftSection={<IconTemplate size={16} />}>
              Asset Templates
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="profiles" pt="md">
            <ProfilesTable />
          </Tabs.Panel>

          <Tabs.Panel value="contacts" pt="md">
            <ContactsTable />
          </Tabs.Panel>

          <Tabs.Panel value="templates" pt="md">
            <AssetTemplatesTable />
          </Tabs.Panel>
        </Tabs>
      </Stack>
   </AdminLayout>
  );
}
