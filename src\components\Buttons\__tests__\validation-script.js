/**
 * Manual validation script for AddToMobileContactButton functionality
 * This script can be run in the browser console to test the component
 */

// Test data
const testContact = {
  name: 'testuser',
  profile: 'Test User',
  description: 'Test Description',
  email: '<EMAIL>',
  phone: '+1234567890',
  website: 'https://example.com',
  images: {
    img1: 'https://example.com/test-image.jpg',
    img2: 'https://example.com/background.jpg',
  },
  social: { twitter: 'testuser', telegram: 'testuser' },
  crypto: { eth: '0x123', btc: '1A1zP1' },
  notes: { 'Note 1': 'Test note content' },
  links: { 'Website': 'https://example.com' },
  image: null,
  uri: null,
  tg_bot: null,
  web2: null,
  web3: null,
  minted: null,
};

// Device detection tests
function testDeviceDetection() {
  console.log('=== Device Detection Tests ===');
  
  const isMobile = () => {
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i;
    const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    return mobileRegex.test(navigator.userAgent) || hasTouchScreen;
  };

  const isIOS = () => {
    const iosRegex = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isPadOS = navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1;
    return iosRegex || isPadOS;
  };

  const isAndroid = () => {
    return /Android/i.test(navigator.userAgent) && !isIOS();
  };

  const supportsWebShare = () => {
    return 'share' in navigator && 'canShare' in navigator;
  };

  console.log('Current device detection:', {
    userAgent: navigator.userAgent,
    isMobile: isMobile(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    supportsWebShare: supportsWebShare(),
    maxTouchPoints: navigator.maxTouchPoints,
    platform: navigator.platform
  });
}

// Base64 conversion test
async function testBase64Conversion() {
  console.log('=== Base64 Conversion Test ===');
  
  const convertImageToBase64 = async (imageUrl) => {
    try {
      if (imageUrl.includes('avatar-2.png')) {
        console.log('Skipping default avatar');
        return null;
      }

      console.log('Converting image to Base64:', imageUrl);
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      console.log('Image blob size:', blob.size, 'type:', blob.type);
      
      if (!blob.type.startsWith('image/')) {
        throw new Error(`Invalid image type: ${blob.type}`);
      }

      if (blob.size > 1024 * 1024) {
        console.warn('Image too large for vCard, skipping:', blob.size);
        return null;
      }

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = reader.result;
          const base64Data = base64.split(',')[1];
          console.log('Successfully converted image to Base64, length:', base64Data.length);
          resolve(base64Data);
        };
        reader.onerror = () => reject(new Error('Failed to convert image to base64'));
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error converting image to base64:', error);
      return null;
    }
  };

  // Test with a small test image
  try {
    const testImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    const result = await convertImageToBase64(testImageUrl);
    console.log('Base64 conversion test result:', result ? 'SUCCESS' : 'FAILED');
  } catch (error) {
    console.error('Base64 conversion test failed:', error);
  }
}

// vCard generation test
async function testVCardGeneration() {
  console.log('=== vCard Generation Test ===');
  
  const generateVCard = async (contact) => {
    let vcard = 'BEGIN:VCARD\n';
    vcard += 'VERSION:3.0\n';

    // Name fields
    vcard += `FN:${contact.profile || contact.name}\n`;
    vcard += `N:${contact.profile || contact.name};;;;\n`;

    // Contact photo - simulate Base64 conversion
    if (contact.images && contact.images.img1) {
      const contactImageUrl = contact.images.img1;
      if (contactImageUrl && !contactImageUrl.includes('avatar-2.png')) {
        try {
          // Simulate Base64 conversion
          const base64Data = 'simulated_base64_data';
          if (base64Data) {
            vcard += `PHOTO;ENCODING=BASE64;TYPE=JPEG:${base64Data}\n`;
          }
        } catch (error) {
          console.log('Could not add photo to vCard:', error);
        }
      }
    }

    // Contact information
    if (contact.phone && contact.phone.trim()) {
      const phoneNumbers = contact.phone.split(/[,\s]+/).filter(phone => phone.trim());
      phoneNumbers.forEach((phone, index) => {
        const cleanPhone = phone.trim();
        if (cleanPhone) {
          const phoneType = index === 0 ? 'CELL' : index === 1 ? 'HOME' : 'WORK';
          vcard += `TEL;TYPE=${phoneType}:${cleanPhone}\n`;
        }
      });
    }

    if (contact.email && contact.email.trim()) {
      vcard += `EMAIL:${contact.email}\n`;
    }

    if (contact.website && contact.website.trim()) {
      vcard += `URL:${contact.website}\n`;
    }

    if (contact.description && contact.description.trim()) {
      vcard += `TITLE:${contact.description}\n`;
    }

    // Social Media Profiles
    if (contact.social) {
      Object.entries(contact.social)
        .filter(([platform, handle]) => handle && handle.trim())
        .forEach(([platform, handle]) => {
          vcard += `X-SOCIALPROFILE;type=${platform}:https://${platform}.com/${handle}\n`;
        });
    }

    // Notes
    if (contact.notes && Object.keys(contact.notes).length > 0) {
      const notesText = Object.entries(contact.notes)
        .filter(([title, content]) => title.trim() && content.trim())
        .map(([title, content]) => `${title}: ${content}`)
        .join('\\n\\n');

      if (notesText) {
        const escapedNotes = notesText
          .replace(/\r\n/g, '\\n')
          .replace(/\n/g, '\\n')
          .replace(/\r/g, '\\n');
        vcard += `NOTE:${escapedNotes}\n`;
      }
    }

    vcard += 'END:VCARD';
    return vcard;
  };

  try {
    const vCardData = await generateVCard(testContact);
    console.log('Generated vCard:');
    console.log(vCardData);
    console.log('vCard generation test: SUCCESS');
    
    // Test vCard validity
    const lines = vCardData.split('\n');
    const hasBegin = lines[0] === 'BEGIN:VCARD';
    const hasEnd = lines[lines.length - 1] === 'END:VCARD';
    const hasVersion = lines.some(line => line.startsWith('VERSION:'));
    const hasFN = lines.some(line => line.startsWith('FN:'));
    
    console.log('vCard validation:', {
      hasBegin,
      hasEnd,
      hasVersion,
      hasFN,
      lineCount: lines.length,
      isValid: hasBegin && hasEnd && hasVersion && hasFN
    });
    
  } catch (error) {
    console.error('vCard generation test failed:', error);
  }
}

// Web Share API test
function testWebShareAPI() {
  console.log('=== Web Share API Test ===');
  
  if ('share' in navigator) {
    console.log('Web Share API is supported');
    
    if ('canShare' in navigator) {
      console.log('canShare method is available');
      
      // Test file sharing capability
      const testBlob = new Blob(['test'], { type: 'text/vcard' });
      const testFile = new File([testBlob], 'test.vcf', { type: 'text/vcard' });
      
      if (navigator.canShare({ files: [testFile] })) {
        console.log('File sharing is supported');
      } else {
        console.log('File sharing is NOT supported');
      }
    } else {
      console.log('canShare method is NOT available');
    }
  } else {
    console.log('Web Share API is NOT supported');
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting AddToMobileContactButton validation tests...\n');
  
  testDeviceDetection();
  console.log('\n');
  
  await testBase64Conversion();
  console.log('\n');
  
  await testVCardGeneration();
  console.log('\n');
  
  testWebShareAPI();
  console.log('\n');
  
  console.log('All tests completed!');
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.validateAddToMobileContact = {
    runAllTests,
    testDeviceDetection,
    testBase64Conversion,
    testVCardGeneration,
    testWebShareAPI
  };
  
  console.log('Validation functions available at window.validateAddToMobileContact');
  console.log('Run window.validateAddToMobileContact.runAllTests() to start testing');
}
