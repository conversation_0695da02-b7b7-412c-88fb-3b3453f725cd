import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { transfer_id, action, response_note } = await request.json();

    // Validate input
    if (!transfer_id || !action) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    if (!['approve', 'decline', 'hide'].includes(action)) {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user is the recipient of this transfer
    const { data: transfer, error: transferError } = await supabase
      .from('asset_transfers')
      .select(`
        *,
        assets (
          id,
          title,
          expiry_date,
          is_deleted
        )
      `)
      .eq('id', transfer_id)
      .eq('to_email', session.user.email)
      .single();

    if (transferError || !transfer) {
      return NextResponse.json({ error: 'Transfer not found or access denied' }, { status: 404 });
    }

    // Check if asset still exists and is not deleted
    if (!transfer.assets || transfer.assets.is_deleted) {
      return NextResponse.json({ error: 'Asset no longer exists' }, { status: 400 });
    }

    // Check if asset is expired (only for approve action)
    if (action === 'approve' && transfer.assets.expiry_date && new Date(transfer.assets.expiry_date) < new Date()) {
      return NextResponse.json({ error: 'Cannot approve expired asset' }, { status: 400 });
    }

    // Check if transfer is in a state that can be responded to
    if (action === 'approve' || action === 'decline') {
      if (transfer.status !== 'pending') {
        return NextResponse.json({ error: 'Transfer has already been responded to' }, { status: 400 });
      }
    } else if (action === 'hide') {
      if (transfer.status !== 'approved') {
        return NextResponse.json({ error: 'Can only hide approved assets' }, { status: 400 });
      }
    }

    // Map action to status
    const statusMap = {
      approve: 'approved',
      decline: 'declined',
      hide: 'hidden'
    };

    const newStatus = statusMap[action as keyof typeof statusMap];

    // Update transfer status
    const updateData: any = {
      status: newStatus,
      response_note: response_note?.trim() || null
    };

    // Set responded_at timestamp for approve/decline actions
    if (action === 'approve' || action === 'decline') {
      updateData.responded_at = new Date().toISOString();
    }

    const { data: updatedTransfer, error: updateError } = await supabase
      .from('asset_transfers')
      .update(updateData)
      .eq('id', transfer_id)
      .select()
      .single();

    if (updateError) {
      console.error('Transfer update error:', updateError);
      return NextResponse.json({ error: 'Failed to update transfer' }, { status: 500 });
    }

    const actionMessages = {
      approve: 'Asset approved successfully',
      decline: 'Asset declined successfully',
      hide: 'Asset hidden successfully'
    };

    return NextResponse.json({ 
      success: true, 
      transfer: updatedTransfer,
      message: actionMessages[action as keyof typeof actionMessages]
    });

  } catch (error) {
    console.error('Asset response error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
