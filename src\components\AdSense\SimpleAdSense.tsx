'use client';

import { useEffect, useRef } from 'react';
import { ENABLE_ADSENSE, ADSENSE_CLIENT_ID } from 'src/lib/config';

interface SimpleAdSenseProps {
  slot: string;
  width?: number;
  height?: number;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export function SimpleAdSense({ slot, width = 300, height = 250 }: SimpleAdSenseProps) {
  const adRef = useRef<HTMLModElement>(null);

  // Don't render if AdSense is disabled or client ID is not set
  if (!ENABLE_ADSENSE || !ADSENSE_CLIENT_ID) {
    return <div style={{ border: '1px solid red', padding: '10px' }}>AdSense Disabled</div>;
  }

  // Don't render if slot is not provided
  if (!slot) {
    return <div style={{ border: '1px solid red', padding: '10px' }}>No Slot Provided</div>;
  }

  useEffect(() => {
    const loadAd = () => {
      try {
        if (typeof window !== 'undefined' && adRef.current) {
          console.log('Loading AdSense ad for slot:', slot);
          window.adsbygoogle = window.adsbygoogle || [];
          window.adsbygoogle.push({});
        }
      } catch (error) {
        console.error('AdSense loading error:', error);
      }
    };

    // Load ad after a short delay
    const timer = setTimeout(loadAd, 500);
    return () => clearTimeout(timer);
  }, [slot]);

  return (
    <div style={{ 
      textAlign: 'center', 
      margin: '20px 0',
      border: '1px dashed #ccc',
      padding: '10px'
    }}>
      <p style={{ fontSize: '12px', color: '#666', margin: '0 0 10px 0' }}>
        AdSense Slot: {slot}
      </p>
      <ins
        ref={adRef}
        className="adsbygoogle"
        style={{
          display: 'block',
          width: `${width}px`,
          height: `${height}px`,
        }}
        data-ad-client={ADSENSE_CLIENT_ID}
        data-ad-slot={slot}
      />
    </div>
  );
}
