/**
 * Client-side admin utilities
 */

export interface AdminAccessInfo {
  isAuthorized: boolean;
  isSuperAdmin: boolean;
  isOwner: boolean;
  ownedPrimaryNames: string[];
  error?: string;
}

/**
 * Check admin access level for the current user
 */
export async function checkAdminAccess(): Promise<AdminAccessInfo> {
  try {
    const response = await fetch('/api/admin/access-check');
    
    if (!response.ok) {
      return {
        isAuthorized: false,
        isSuperAdmin: false,
        isOwner: false,
        ownedPrimaryNames: [],
        error: 'Failed to check admin access'
      };
    }

    const data = await response.json();
    return {
      isAuthorized: data.isAuthorized || false,
      isSuperAdmin: data.isSuperAdmin || false,
      isOwner: data.isOwner || false,
      ownedPrimaryNames: data.ownedPrimaryNames || [],
      error: data.error
    };
  } catch (error) {
    return {
      isAuthorized: false,
      isSuperAdmin: false,
      isOwner: false,
      ownedPrimaryNames: [],
      error: 'Network error checking admin access'
    };
  }
}
