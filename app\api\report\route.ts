import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { SMTP_CONFIG, CONTACT_EMAIL } from 'src/lib/config';

// Simple in-memory rate limiting (in production, use Redis or database)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 3; // Max 3 requests per 15 minutes per IP

// Sanitize input to prevent XSS
function sanitizeInput(input: string): string {
  if (!input) return '';
  return input
    .replace(/[<>]/g, '') // Remove < and > characters
    .trim()
    .substring(0, 1000); // Limit length
}

// Rate limiting function
function checkRateLimit(ip: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return { allowed: true };
  }

  if (now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return { allowed: true };
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, resetTime: userLimit.resetTime };
  }

  userLimit.count++;
  return { allowed: true };
}

export async function POST(request: NextRequest) {
  try {
    // Check request size (prevent DoS attacks)
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > 10000) { // 10KB limit
      return NextResponse.json(
        { error: 'Request too large' },
        { status: 413 }
      );
    }

    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    // Check rate limit
    const rateLimit = checkRateLimit(ip);
    if (!rateLimit.allowed) {
      const resetTime = rateLimit.resetTime ? new Date(rateLimit.resetTime).toISOString() : 'unknown';
      return NextResponse.json(
        {
          error: 'Too many requests. Please try again later.',
          resetTime: resetTime
        },
        { status: 429 }
      );
    }

    const { name, email, reportType, profileName, subject, message, mathAnswer, expectedAnswer } = await request.json();

    // Sanitize inputs
    const sanitizedName = sanitizeInput(name);
    const sanitizedEmail = sanitizeInput(email);
    const sanitizedReportType = sanitizeInput(reportType);
    const sanitizedProfileName = sanitizeInput(profileName);
    const sanitizedSubject = sanitizeInput(subject || '');
    const sanitizedMessage = sanitizeInput(message);

    // Validate required fields
    if (!sanitizedName || !sanitizedEmail || !sanitizedReportType || !sanitizedProfileName || !sanitizedMessage) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(sanitizedEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate math captcha
    if (!mathAnswer || !expectedAnswer || Number(mathAnswer) !== Number(expectedAnswer)) {
      return NextResponse.json(
        { error: 'Invalid captcha answer. Please solve the math problem correctly.' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: SMTP_CONFIG.host,
      port: SMTP_CONFIG.port,
      secure: SMTP_CONFIG.secure,
      auth: {
        user: SMTP_CONFIG.auth.user,
        pass: SMTP_CONFIG.auth.pass,
      },
    });

    // Get report type display name
    const reportTypeMap: { [key: string]: string } = {
      'inappropriate_content': 'Inappropriate Content',
      'impersonation': 'Impersonation',
      'copyright_violation': 'Copyright Violation',
      'spam': 'Spam',
      'claim_ownership': 'Claim Ownership',
      'other': 'Other'
    };

    const reportTypeDisplay = reportTypeMap[sanitizedReportType] || sanitizedReportType;

    // Email content
    const mailOptions = {
      from: `"ODude Report System" <${SMTP_CONFIG.auth.user}>`,
      to: CONTACT_EMAIL,
      subject: `Profile Report: ${reportTypeDisplay} - ${sanitizedProfileName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
            🚨 Profile Report Submission
          </h2>
          
          <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="color: #856404; margin-top: 0;">Report Details</h3>
            <p><strong>Reporter Name:</strong> ${sanitizedName}</p>
            <p><strong>Reporter Email:</strong> ${sanitizedEmail}</p>
            <p><strong>Report Type:</strong> ${reportTypeDisplay}</p>
            <p><strong>Profile Reported:</strong> ${sanitizedProfileName}</p>
            <p><strong>Subject:</strong> ${sanitizedSubject || 'No subject provided'}</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Detailed Report</h3>
            <p style="white-space: pre-wrap; line-height: 1.6;">${sanitizedMessage}</p>
          </div>

          <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #17a2b8;">
            <h4 style="color: #0c5460; margin-top: 0;">Next Steps</h4>
            <ul style="color: #0c5460;">
              <li>Review the reported profile: <strong>${sanitizedProfileName}</strong></li>
              <li>Investigate the claims made in this report</li>
              <li>Take appropriate action if violations are confirmed</li>
              <li>Consider contacting the reporter if more information is needed</li>
            </ul>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center;">
            <p style="color: #6c757d; font-size: 12px;">
              This report was submitted through the ODude Report System<br>
              IP Address: ${ip}<br>
              Timestamp: ${new Date().toISOString()}
            </p>
          </div>
        </div>
      `,
    };

    // Send email
    await transporter.sendMail(mailOptions);

    const response = NextResponse.json(
      { message: 'Report submitted successfully' },
      { status: 200 }
    );

    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');

    return response;

  } catch (error) {
    console.error('Report submission error:', error);
    
    return NextResponse.json(
      { error: 'Failed to submit report' },
      { status: 500 }
    );
  }
}
