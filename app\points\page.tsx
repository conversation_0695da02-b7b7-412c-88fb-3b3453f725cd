'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  Container,
  Title,
  Card,
  Table,
  Text,
  Badge,
  Group,
  Select,
  TextInput,
  Button,
  Stack,
  Alert,
  Pagination,
  LoadingOverlay,
  Divider,
  ActionIcon,
  Tooltip,
  ScrollArea
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useDebouncedValue } from '@mantine/hooks';
import { 
  IconCoins, 
  IconSearch, 
  IconRefresh, 
  IconArrowUp, 
  IconArrowDown,
  IconArrowsExchange,
  IconGift,
  IconShoppingCart,
  IconBookmark,
  IconUserPlus
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { getUserTransactionHistory, getUserPoints, type TransactionLog } from '../../src/lib/points';
import { useAuthRedirect } from '../../src/hooks/useAuthRedirect';
import FullLayout from '../../src/components/layouts/FullLayout';

const ITEMS_PER_PAGE = 20;

const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'SIGNUP':
      return <IconUserPlus size={16} color="green" />;
    case 'CREATE_CONTACT':
      return <IconShoppingCart size={16} color="red" />;
    case 'TRANSFER_SEND':
      return <IconArrowUp size={16} color="red" />;
    case 'TRANSFER_RECEIVE':
      return <IconArrowDown size={16} color="green" />;
    case 'TRANSFER_ROLLBACK':
      return <IconRefresh size={16} color="gray" />;
    default:
      return <IconCoins size={16} />;
  }
};

const getTransactionColor = (type: string, pointsChange: number) => {
  if (pointsChange > 0) return 'green';
  if (pointsChange < 0) return 'red';
  return 'gray';
};

const formatTransactionType = (type: string) => {
  switch (type) {
    case 'SIGNUP':
      return 'Signup Bonus';
    case 'CREATE_CONTACT':
      return 'Contact Creation';
    case 'TRANSFER_SEND':
      return 'Points Sent';
    case 'TRANSFER_RECEIVE':
      return 'Points Received';
    case 'TRANSFER_ROLLBACK':
      return 'Transfer Rollback';
    default:
      return type;
  }
};

export default function PointsHistoryPage() {
  const { data: session, status } = useSession();
  const { isLoading } = useAuthRedirect();
  
  const [transactions, setTransactions] = useState<TransactionLog[]>([]);
  const [currentPoints, setCurrentPoints] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Filters
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  
  const [debouncedSearch] = useDebouncedValue(searchQuery, 300);

  const fetchTransactions = useCallback(async (page: number = 1) => {
    const emailToFetch = session?.user?.email;
    if (!emailToFetch) return;
    
    setLoading(true);
    try {
      // Get user's current points
      const points = await getUserPoints(emailToFetch);
      setCurrentPoints(points);

      // Get transaction history
      const history = await getUserTransactionHistory(emailToFetch, ITEMS_PER_PAGE * 10); // Get more for filtering
      
      // Apply filters
      let filteredHistory = history;
      
      if (typeFilter) {
        filteredHistory = filteredHistory.filter(t => t.transaction_type === typeFilter);
      }
      
      if (debouncedSearch) {
        filteredHistory = filteredHistory.filter(t => 
          t.description?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
          t.reference_id?.toLowerCase().includes(debouncedSearch.toLowerCase())
        );
      }
      
      if (dateFrom) {
        const startDate = new Date(dateFrom);
        filteredHistory = filteredHistory.filter(t =>
          new Date(t.created_at) >= startDate
        );
      }

      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999);
        filteredHistory = filteredHistory.filter(t =>
          new Date(t.created_at) <= endDate
        );
      }
      
      // Paginate
      const startIndex = (page - 1) * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedTransactions = filteredHistory.slice(startIndex, endIndex);
      
      setTransactions(paginatedTransactions);
      setTotalPages(Math.ceil(filteredHistory.length / ITEMS_PER_PAGE));
      
    } catch (error) {
      console.error('Error fetching transactions:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load transaction history',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, [session?.user?.email, typeFilter, debouncedSearch, dateFrom, dateTo]);

  useEffect(() => {
    if (session?.user?.email) {
      setCurrentPage(1);
      fetchTransactions(1);
    }
  }, [fetchTransactions]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchTransactions(page);
  };

  const clearFilters = () => {
    setTypeFilter('');
    setSearchQuery('');
    setDateFrom('');
    setDateTo('');
    setCurrentPage(1);
  };

  if (isLoading || status === 'loading') {
    return (
      <FullLayout>
        <Container size="lg" py="xl">
          <LoadingOverlay visible />
        </Container>
      </FullLayout>
    );
  }

  if (!session) {
    return null;
  }

  const transactionTypeOptions = [
    { value: '', label: 'All Types' },
    { value: 'SIGNUP', label: 'Signup Bonus' },
    { value: 'CREATE_CONTACT', label: 'Contact Creation' },
    { value: 'TRANSFER_SEND', label: 'Points Sent' },
    { value: 'TRANSFER_RECEIVE', label: 'Points Received' },
  ];

  return (
    <FullLayout>
      <Container size="lg" py="xl">
        <Stack gap="lg">
          <Group justify="space-between" align="center">
            <Title order={2}>Points History</Title>
            <Card withBorder p="md">
              <Group gap="xs" align="center">
                <IconCoins size={20} color="blue" />
                <Text size="sm" c="dimmed">Current Balance:</Text>
                <Badge size="lg" variant="filled" color="blue">
                  {currentPoints.toLocaleString()} points
                </Badge>
              </Group>
            </Card>
          </Group>

          <Card withBorder>
            <Stack gap="md">
              <Text fw={500}>Filters</Text>



              <Group grow>
                <Select
                  label="Transaction Type"
                  placeholder="Filter by type"
                  data={transactionTypeOptions}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                  clearable
                />
                
                <TextInput
                  label="Search"
                  placeholder="Search description or reference"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftSection={<IconSearch size={16} />}
                />
              </Group>
              
              <Group grow>
                <DatePickerInput
                  label="From Date"
                  placeholder="Select start date"
                  value={dateFrom ? new Date(dateFrom) : null}
                  onChange={(value) => setDateFrom(value ? value.toString().split('T')[0] : '')}
                  clearable
                />

                <DatePickerInput
                  label="To Date"
                  placeholder="Select end date"
                  value={dateTo ? new Date(dateTo) : null}
                  onChange={(value) => setDateTo(value ? value.toString().split('T')[0] : '')}
                  clearable
                />
              </Group>
              
              <Group justify="flex-end">
                <Button variant="subtle" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={() => fetchTransactions(currentPage)} leftSection={<IconRefresh size={16} />}>
                  Refresh
                </Button>
              </Group>
            </Stack>
          </Card>

          <Card withBorder style={{ position: 'relative' }}>
            <LoadingOverlay visible={loading} />

            <ScrollArea>
              <Table striped highlightOnHover style={{ minWidth: '600px' }}>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th style={{ minWidth: '100px' }}>Date</Table.Th>
                    <Table.Th style={{ minWidth: '120px' }}>Type</Table.Th>
                    <Table.Th style={{ minWidth: '150px' }}>Description</Table.Th>
                    <Table.Th style={{ minWidth: '80px' }}>Points</Table.Th>
                    <Table.Th style={{ minWidth: '80px' }}>Balance</Table.Th>
                  </Table.Tr>
                </Table.Thead>
              <Table.Tbody>
                {transactions.length === 0 ? (
                  <Table.Tr>
                    <Table.Td colSpan={5}>
                      <Text ta="center" c="dimmed" py="xl">
                        No transactions found
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  transactions.map((transaction) => (
                    <Table.Tr key={transaction.id}>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(transaction.created_at).toLocaleDateString()}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {new Date(transaction.created_at).toLocaleTimeString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          {getTransactionIcon(transaction.transaction_type)}
                          <Text size="sm">
                            {formatTransactionType(transaction.transaction_type)}
                          </Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {transaction.description || 'No description'}
                        </Text>
                        {transaction.reference_id && (
                          <Text size="xs" c="dimmed">
                            Ref: {transaction.reference_id}
                          </Text>
                        )}
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={getTransactionColor(transaction.transaction_type, transaction.points_change)}
                          variant="light"
                        >
                          {transaction.points_change > 0 ? '+' : ''}{transaction.points_change.toLocaleString()}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm" fw={500}>
                          {transaction.points_after.toLocaleString()}
                        </Text>
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>
            </ScrollArea>

            {totalPages > 1 && (
              <>
                <Divider my="md" />
                <Group justify="center">
                  <Pagination
                    value={currentPage}
                    onChange={handlePageChange}
                    total={totalPages}
                    size="sm"
                  />
                </Group>
              </>
            )}
          </Card>
        </Stack>
      </Container>
    </FullLayout>
  );
}
