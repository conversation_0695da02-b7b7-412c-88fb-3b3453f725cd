import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { verifyAdminAuth, filterContactsByOwnerNamespace } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;
    const search = searchParams.get('search') || '';
    const profileFilter = searchParams.get('profile') || '';
    const imageFilter = searchParams.get('image') || '';
    const mintedFilter = searchParams.get('minted') || '';

    const supabase = getSupabaseAdminClient();

    // Build query
    let query = supabase
      .from('contact')
      .select(`
        timestamp,
        name,
        profile,
        image,
        minted,
        profile_email,
        disabled
      `)
      .order('timestamp', { ascending: false });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,profile.ilike.%${search}%`);
    }

    if (profileFilter) {
      query = query.eq('profile_email', profileFilter);
    }

    if (imageFilter) {
      if (imageFilter === 'yes') {
        query = query.not('image', 'is', null);
      } else if (imageFilter === 'no') {
        query = query.is('image', null);
      }
    }

    if (mintedFilter) {
      if (mintedFilter === 'yes') {
        query = query.not('minted', 'is', null);
      } else if (mintedFilter === 'no') {
        query = query.is('minted', null);
      }
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: contacts, error: contactsError } = await query;

    if (contactsError) {
      console.error('Error fetching contacts:', contactsError);
      return NextResponse.json({ error: 'Failed to fetch contacts' }, { status: 500 });
    }

    // Filter contacts by ownership if user is not super admin
    let filteredContacts = contacts || [];
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      filteredContacts = filterContactsByOwnerNamespace(filteredContacts, authResult.ownedPrimaryNames);
    }

    // Get total count for pagination (with same filters)
    let countQuery = supabase
      .from('contact')
      .select('*', { count: 'exact', head: true });

    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,profile.ilike.%${search}%`);
    }

    if (profileFilter) {
      countQuery = countQuery.eq('profile_email', profileFilter);
    }

    if (imageFilter) {
      if (imageFilter === 'yes') {
        countQuery = countQuery.not('image', 'is', null);
      } else if (imageFilter === 'no') {
        countQuery = countQuery.is('image', null);
      }
    }

    if (mintedFilter) {
      if (mintedFilter === 'yes') {
        countQuery = countQuery.not('minted', 'is', null);
      } else if (mintedFilter === 'no') {
        countQuery = countQuery.is('minted', null);
      }
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      console.error('Error fetching contacts count:', countError);
      return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
    }

    // Calculate filtered count for owners
    let finalCount = totalCount || 0;
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      // For owners, we need to count only their namespace contacts
      // Since we already filtered the data, use the filtered count
      finalCount = filteredContacts.length;
    }

    return NextResponse.json({
      data: filteredContacts,
      pagination: {
        page,
        limit,
        total: finalCount,
        totalPages: Math.ceil(finalCount / limit),
      },
    });

  } catch (error) {
    console.error('Admin contacts API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { contactName, profileName, profileEmail } = await request.json();

    if (!contactName || !profileName || !profileEmail) {
      return NextResponse.json({ error: 'Contact name, profile name, and profile email are required' }, { status: 400 });
    }

    // Validate contact name format (should be name@primaryname)
    const nameParts = contactName.split('@');
    if (nameParts.length !== 2) {
      return NextResponse.json({ error: 'Contact name must be in format: name@primaryname' }, { status: 400 });
    }

    const [username, primaryName] = nameParts;
    if (!username || !primaryName) {
      return NextResponse.json({ error: 'Invalid contact name format' }, { status: 400 });
    }

    // Check ownership permissions for non-super admins
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      if (!authResult.ownedPrimaryNames.includes(primaryName)) {
        return NextResponse.json({ error: 'Unauthorized - you can only create contacts in your owned namespaces' }, { status: 403 });
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(profileEmail)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Check if contact name already exists
    const { data: existing, error: checkError } = await supabase
      .from('contact')
      .select('name')
      .eq('name', contactName.toLowerCase())
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking existing contact:', checkError);
      return NextResponse.json({ error: 'Failed to check existing contact' }, { status: 500 });
    }

    if (existing) {
      return NextResponse.json({ error: 'Contact name already exists' }, { status: 400 });
    }

    // Create new contact
    const contactData = {
      name: contactName.toLowerCase(),
      profile: profileName,
      profile_email: profileEmail,
      timestamp: new Date().toISOString(),
    };

    const { data: newContact, error: insertError } = await supabase
      .from('contact')
      .insert(contactData)
      .select()
      .single();

    if (insertError) {
      console.error('Error creating contact:', insertError);
      return NextResponse.json({ error: 'Failed to create contact' }, { status: 500 });
    }

    return NextResponse.json(newContact);
  } catch (error) {
    console.error('Admin contact POST API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { contactName, action } = await request.json();

    if (!contactName) {
      return NextResponse.json({ error: 'Contact name is required' }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({ error: 'Action is required' }, { status: 400 });
    }

    // Check ownership permissions for non-super admins
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      const { isContactInOwnerNamespace } = await import('src/lib/adminAuth');
      if (!isContactInOwnerNamespace(contactName, authResult.ownedPrimaryNames)) {
        return NextResponse.json({ error: 'Unauthorized - contact not in your namespace' }, { status: 403 });
      }
    }

    const supabase = getSupabaseAdminClient();

    // Handle different actions
    if (action === 'clearImage') {
      // Clear the image field (set to null)
      const { error: updateError } = await supabase
        .from('contact')
        .update({ image: null })
        .eq('name', contactName.toLowerCase());

      if (updateError) {
        console.error('Error clearing image:', updateError);
        return NextResponse.json({ error: 'Failed to clear image status' }, { status: 500 });
      }

      return NextResponse.json({ success: true, message: 'Image status cleared successfully' });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Admin patch contact API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { contactName } = await request.json();

    if (!contactName) {
      return NextResponse.json({ error: 'Contact name is required' }, { status: 400 });
    }

    // Check ownership permissions for non-super admins
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      const { isContactInOwnerNamespace } = await import('src/lib/adminAuth');
      if (!isContactInOwnerNamespace(contactName, authResult.ownedPrimaryNames)) {
        return NextResponse.json({ error: 'Unauthorized - contact not in your namespace' }, { status: 403 });
      }
    }

    const supabase = getSupabaseAdminClient();

    // Get contact details first for cleanup
    const { data: contact, error: fetchError } = await supabase
      .from('contact')
      .select('*')
      .eq('name', contactName.toLowerCase())
      .single();

    if (fetchError) {
      console.error('Error fetching contact:', fetchError);
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Delete associated bookmarks
    const { error: bookmarkError } = await supabase
      .from('bookmark')
      .delete()
      .eq('contact_name', contactName.toLowerCase());

    if (bookmarkError) {
      console.error('Error deleting bookmarks:', bookmarkError);
    }

    // Delete the contact
    const { error: deleteError } = await supabase
      .from('contact')
      .delete()
      .eq('name', contactName.toLowerCase());

    if (deleteError) {
      console.error('Error deleting contact:', deleteError);
      return NextResponse.json({ error: 'Failed to delete contact' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Contact deleted successfully' });

  } catch (error) {
    console.error('Admin delete contact API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
