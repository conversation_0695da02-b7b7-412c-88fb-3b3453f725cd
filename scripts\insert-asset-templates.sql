-- Insert asset templates for the SVG-based template system
-- These templates use SVG code with dynamic placeholders

-- Insert Badge template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'badge_default',
  'Default Badge Template',
  'Standard badge template with circular design and gradient background',
  'Badge',
  '<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
    <defs>
      <linearGradient id="badgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
      </linearGradient>
    </defs>
    <circle cx="150" cy="150" r="140" fill="url(#badgeGradient)" stroke="#fff" stroke-width="4"/>
    <circle cx="150" cy="150" r="100" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <text x="150" y="130" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="18" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="150" y="155" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Inter, Arial, sans-serif" font-size="14">
      Awarded to
    </text>
    <text x="150" y="180" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="600">
      {{full_name}}
    </text>
    <text x="150" y="200" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
      {{issued_date}}
    </text>
  </svg>',
  true,
  NOW()
);

-- Insert Certificate template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'certificate_default',
  'Default Certificate Template',
  'Professional certificate template with decorative borders',
  'Certificate',
  '<svg xmlns="http://www.w3.org/2000/svg" width="500" height="350" viewBox="0 0 500 350">
    <rect width="500" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="3"/>
    <rect x="20" y="20" width="460" height="310" fill="white" stroke="#6c757d" stroke-width="1"/>
    <rect x="30" y="30" width="440" height="40" fill="#495057"/>
    <text x="250" y="55" text-anchor="middle" fill="white" font-family="Georgia, serif" font-size="24" font-weight="bold">
      CERTIFICATE OF {{asset_title}}
    </text>
    <text x="250" y="120" text-anchor="middle" fill="#495057" font-family="Georgia, serif" font-size="16">
      This is to certify that
    </text>
    <text x="250" y="160" text-anchor="middle" fill="#212529" font-family="Georgia, serif" font-size="28" font-weight="bold">
      {{full_name}}
    </text>
    <text x="250" y="200" text-anchor="middle" fill="#495057" font-family="Georgia, serif" font-size="14">
      {{asset_description}}
    </text>
    <text x="100" y="280" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="12">
      Date: {{issued_date}}
    </text>
    <text x="400" y="280" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="12">
      Issued by: {{profile_name}}
    </text>
    <line x1="200" y1="290" x2="300" y2="290" stroke="#6c757d" stroke-width="1"/>
    <text x="250" y="305" text-anchor="middle" fill="#6c757d" font-family="Georgia, serif" font-size="10">
      Authorized Signature
    </text>
  </svg>',
  true,
  NOW()
);

-- Insert Ticket template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'ticket_default',
  'Default Ticket Template',
  'Event ticket template with perforated edge design',
  'Ticket',
  '<svg xmlns="http://www.w3.org/2000/svg" width="400" height="180" viewBox="0 0 400 180">
    <rect width="300" height="180" fill="#ff6b6b" rx="10"/>
    <rect x="300" y="0" width="100" height="180" fill="#ff5252" rx="0 10 10 0"/>
    <line x1="300" y1="0" x2="300" y2="180" stroke="white" stroke-width="2" stroke-dasharray="5,5"/>
    <text x="150" y="40" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="20" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="150" y="70" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Inter, Arial, sans-serif" font-size="14">
      {{asset_description}}
    </text>
    <text x="150" y="100" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="12">
      Ticket Holder: {{full_name}}
    </text>
    <text x="150" y="125" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
      Event Date: {{issued_date}}
    </text>
    <text x="150" y="145" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, Arial, sans-serif" font-size="10">
      Valid until: {{expire_date}}
    </text>
    <text x="350" y="90" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="8" transform="rotate(90 350 90)">
      ADMIT ONE
    </text>
  </svg>',
  true,
  NOW()
);

-- Insert Coupon template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'coupon_default',
  'Default Coupon Template',
  'Discount coupon template with wavy borders',
  'Coupon',
  '<svg xmlns="http://www.w3.org/2000/svg" width="450" height="200" viewBox="0 0 450 200">
    <defs>
      <pattern id="wavyBorder" patternUnits="userSpaceOnUse" width="20" height="20">
        <path d="M0,10 Q5,0 10,10 Q15,20 20,10" stroke="#e74c3c" stroke-width="2" fill="none"/>
      </pattern>
    </defs>
    <rect width="450" height="200" fill="#fff5f5" stroke="url(#wavyBorder)" stroke-width="3"/>
    <rect x="20" y="20" width="410" height="160" fill="white" stroke="#e74c3c" stroke-width="1" stroke-dasharray="5,5"/>
    <text x="225" y="60" text-anchor="middle" fill="#e74c3c" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="225" y="90" text-anchor="middle" fill="#c0392b" font-family="Inter, Arial, sans-serif" font-size="16">
      {{asset_description}}
    </text>
    <text x="225" y="120" text-anchor="middle" fill="#7f8c8d" font-family="Inter, Arial, sans-serif" font-size="12">
      Valid for: {{full_name}}
    </text>
    <text x="100" y="160" text-anchor="middle" fill="#95a5a6" font-family="Inter, Arial, sans-serif" font-size="10">
      Issued: {{issued_date}}
    </text>
    <text x="350" y="160" text-anchor="middle" fill="#95a5a6" font-family="Inter, Arial, sans-serif" font-size="10">
      Expires: {{expire_date}}
    </text>
    <circle cx="50" cy="100" r="15" fill="#e74c3c"/>
    <text x="50" y="105" text-anchor="middle" fill="white" font-family="Inter, Arial, sans-serif" font-size="12" font-weight="bold">%</text>
  </svg>',
  true,
  NOW()
);

-- Insert additional Badge template (Premium style)
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'badge_premium',
  'Premium Badge Template',
  'Premium badge template with gold accents and enhanced design',
  'Badge',
  '<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
    <defs>
      <radialGradient id="premiumGradient" cx="50%" cy="30%" r="70%">
        <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#ffb347;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#cd853f;stop-opacity:1" />
      </radialGradient>
    </defs>
    <circle cx="150" cy="150" r="140" fill="url(#premiumGradient)" stroke="#8b4513" stroke-width="6"/>
    <circle cx="150" cy="150" r="110" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.5)" stroke-width="3"/>
    <text x="150" y="120" text-anchor="middle" fill="#8b4513" font-family="Georgia, serif" font-size="16" font-weight="bold">
      PREMIUM
    </text>
    <text x="150" y="145" text-anchor="middle" fill="#8b4513" font-family="Georgia, serif" font-size="20" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="150" y="170" text-anchor="middle" fill="#654321" font-family="Georgia, serif" font-size="14">
      Awarded to
    </text>
    <text x="150" y="195" text-anchor="middle" fill="#8b4513" font-family="Georgia, serif" font-size="18" font-weight="600">
      {{full_name}}
    </text>
    <text x="150" y="215" text-anchor="middle" fill="#654321" font-family="Georgia, serif" font-size="10">
      {{issued_date}}
    </text>
  </svg>',
  true,
  NOW()
);

-- Insert additional Certificate template (Academic style)
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  svg_code,
  is_active,
  created_at
) VALUES
(
  'certificate_academic',
  'Academic Certificate Template',
  'Academic certificate template with institutional styling',
  'Certificate',
  '<svg xmlns="http://www.w3.org/2000/svg" width="500" height="350" viewBox="0 0 500 350">
    <rect width="500" height="350" fill="#f0f8ff" stroke="#1e40af" stroke-width="4"/>
    <rect x="15" y="15" width="470" height="320" fill="white" stroke="#3b82f6" stroke-width="2"/>
    <rect x="30" y="30" width="440" height="50" fill="#1e40af"/>
    <text x="250" y="60" text-anchor="middle" fill="white" font-family="Georgia, serif" font-size="26" font-weight="bold">
      ACADEMIC CERTIFICATE
    </text>
    <text x="250" y="130" text-anchor="middle" fill="#1e40af" font-family="Georgia, serif" font-size="18">
      This certifies that
    </text>
    <text x="250" y="170" text-anchor="middle" fill="#1f2937" font-family="Georgia, serif" font-size="32" font-weight="bold">
      {{full_name}}
    </text>
    <text x="250" y="210" text-anchor="middle" fill="#1e40af" font-family="Georgia, serif" font-size="16">
      has successfully completed
    </text>
    <text x="250" y="240" text-anchor="middle" fill="#059669" font-family="Georgia, serif" font-size="20" font-weight="bold">
      {{asset_title}}
    </text>
    <text x="250" y="270" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="14">
      {{asset_description}}
    </text>
    <text x="120" y="310" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="12">
      Date: {{issued_date}}
    </text>
    <text x="380" y="310" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="12">
      Institution: {{profile_name}}
    </text>
    <line x1="180" y1="320" x2="320" y2="320" stroke="#1e40af" stroke-width="2"/>
    <text x="250" y="335" text-anchor="middle" fill="#6b7280" font-family="Georgia, serif" font-size="10">
      Authorized Academic Signature
    </text>
  </svg>',
  true,
  NOW()
);

-- Verify the inserted templates
SELECT
  id,
  name,
  asset_type,
  is_active,
  created_at
FROM asset_templates
ORDER BY asset_type, name;
