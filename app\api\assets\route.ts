import { NextRequest, NextResponse } from 'next/server';
import { auth } from 'auth';
import { getSupabaseClient } from 'src/lib/supabase';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { validateAssetData } from 'src/lib/assets-utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const assetData = await request.json();

    // Set issuer information
    assetData.issuer_email = session.user.email;

    // Get user's primary ODude name from ownership data
    if (!assetData.issuer_odude_name) {
      const supabase = getSupabaseClient();

      // Check if user owns any primary names
      const { data: ownedNames, error: ownerError } = await supabase
        .from('primary_name_owners')
        .select('owner_of')
        .eq('user_email', session.user.email)
        .limit(1);

      if (ownerError || !ownedNames || ownedNames.length === 0) {
        return NextResponse.json({
          error: 'You must own at least one primary name to create assets. Please contact an administrator.'
        }, { status: 403 });
      }

      // Use the first owned primary name as the issuer ODude name
      const primaryName = ownedNames[0].owner_of;
      assetData.issuer_odude_name = `${session.user.email.split('@')[0]}@${primaryName}`;
    }

    // Validate asset data
    const validationErrors = validateAssetData(assetData);
    if (validationErrors.length > 0) {
      return NextResponse.json({ error: validationErrors.join(', ') }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Insert asset
    const { data: asset, error: insertError } = await supabase
      .from('assets')
      .insert(assetData)
      .select()
      .single();

    if (insertError) {
      console.error('Asset creation error:', insertError);
      return NextResponse.json({ error: 'Failed to create asset' }, { status: 500 });
    }

    return NextResponse.json({ success: true, asset });

  } catch (error) {
    console.error('Create asset error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const includeStats = searchParams.get('stats') === 'true';

    const supabase = getSupabaseClient();

    let query;
    
    if (includeStats) {
      // Use the view that includes transfer statistics
      query = supabase
        .from('assets_with_stats')
        .select('*')
        .eq('issuer_email', session.user.email)
        .eq('is_deleted', false);
    } else {
      // Use regular assets table
      query = supabase
        .from('assets')
        .select('*')
        .eq('issuer_email', session.user.email)
        .eq('is_deleted', false);
    }

    query = query.order('created_at', { ascending: false });

    const { data: assets, error } = await query;

    if (error) {
      console.error('Fetch assets error:', error);
      return NextResponse.json({ error: 'Failed to fetch assets' }, { status: 500 });
    }

    return NextResponse.json({ assets });

  } catch (error) {
    console.error('Get assets error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id, ...updateData } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Asset ID is required' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user owns the asset
    const { data: existingAsset, error: fetchError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', id)
      .eq('issuer_email', session.user.email)
      .eq('is_deleted', false)
      .single();

    if (fetchError || !existingAsset) {
      return NextResponse.json({ error: 'Asset not found or access denied' }, { status: 404 });
    }

    // Remove fields that shouldn't be updated
    delete updateData.id;
    delete updateData.issuer_email;
    delete updateData.issuer_odude_name;
    delete updateData.created_at;

    // Set updated timestamp
    updateData.updated_at = new Date().toISOString();

    // Validate updated data
    const mergedData = { ...existingAsset, ...updateData };
    const validationErrors = validateAssetData(mergedData);
    if (validationErrors.length > 0) {
      return NextResponse.json({ error: validationErrors.join(', ') }, { status: 400 });
    }

    // Update asset
    const { data: updatedAsset, error: updateError } = await supabase
      .from('assets')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Asset update error:', updateError);
      return NextResponse.json({ error: 'Failed to update asset' }, { status: 500 });
    }

    return NextResponse.json({ success: true, asset: updatedAsset });

  } catch (error) {
    console.error('Update asset error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Asset ID is required' }, { status: 400 });
    }

    const supabase = getSupabaseAdminClient();

    // Verify the user owns the asset
    const { data: existingAsset, error: fetchError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', id)
      .eq('issuer_email', session.user.email)
      .eq('is_deleted', false)
      .single();

    if (fetchError || !existingAsset) {
      return NextResponse.json({ error: 'Asset not found or access denied' }, { status: 404 });
    }

    // Delete the uploaded image from storage
    if (existingAsset.image_url) {
      try {
        // Extract file path from URL
        const url = new URL(existingAsset.image_url);
        const pathParts = url.pathname.split('/');
        // Find the path after 'storage/v1/object/public/images/'
        const storageIndex = pathParts.findIndex(part => part === 'images');
        if (storageIndex !== -1 && storageIndex < pathParts.length - 1) {
          const filePath = pathParts.slice(storageIndex + 1).join('/');

          const { error: storageError } = await supabase.storage
            .from('images')
            .remove([filePath]);

          if (storageError) {
            console.error('Error deleting image from storage:', storageError);
            // Continue with asset deletion even if image deletion fails
          }
        }
      } catch (error) {
        console.error('Error parsing image URL for deletion:', error);
        // Continue with asset deletion even if image deletion fails
      }
    }

    // Delete all asset transfers related to this asset
    const { error: transferDeleteError } = await supabase
      .from('asset_transfers')
      .delete()
      .eq('asset_id', id);

    if (transferDeleteError) {
      console.error('Error deleting asset transfers:', transferDeleteError);
      // Continue with asset deletion even if transfer deletion fails
    }

    // Soft delete the asset (this will remove it from everywhere, including sent copies)
    const { error: deleteError } = await supabase
      .from('assets')
      .update({
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (deleteError) {
      console.error('Asset deletion error:', deleteError);
      return NextResponse.json({ error: 'Failed to delete asset' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Asset and all references deleted successfully' });

  } catch (error) {
    console.error('Delete asset error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
