'use client';

import { useEffect, useRef, useState } from 'react';
import { Box, Text } from '@mantine/core';
import { ENABLE_ADSENSE, ADSENSE_CLIENT_ID } from 'src/lib/config';
import { usePathname } from 'next/navigation';

interface AdSenseBannerProps {
  slot: string;
  width?: number | string;
  height?: number | string;
  responsive?: boolean;
  style?: 'display-block' | 'display-inline-block';
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export function AdSenseBanner({
  slot,
  width = '728px',
  height = '90px',
  responsive = true,
  style = 'display-inline-block',
  className,
}: AdSenseBannerProps) {
  const pathname = usePathname();
  const containerRef = useRef<HTMLDivElement>(null);
  const [adUnfilled, setAdUnfilled] = useState(false);
  const [adChecked, setAdChecked] = useState(false); // track if we finished checking

  useEffect(() => {
    const ins = containerRef.current?.querySelector('ins.adsbygoogle') as HTMLElement | null;

    if (!ins) return;

    setAdUnfilled(false);
    setAdChecked(false);
    ins.removeAttribute('data-adsbygoogle-status');

    try {
      (window.adsbygoogle = window.adsbygoogle || []).push({});
      console.log('AdSense: Ad pushed');

      const timeout = setTimeout(() => {
        const status = ins.getAttribute('data-ad-status');
        if (status === 'unfilled') {
          setAdUnfilled(true);
          console.warn('AdSense: Ad not filled');
        }
        setAdChecked(true);
      }, 2000); // give it time to try loading

      return () => clearTimeout(timeout);
    } catch (error) {
      console.error('AdSense error:', error);
    }
  }, [slot, pathname]);

  if (!ENABLE_ADSENSE || !ADSENSE_CLIENT_ID || !slot) return null;

  return (
    <Box ref={containerRef} className={className} style={{ textAlign: 'center', margin: '20px 0' }}>
      {!adUnfilled && (
        <ins
          className="adsbygoogle"
          style={{
            display: style === 'display-inline-block' ? 'inline-block' : 'block',
            width: responsive ? '100%' : typeof width === 'number' ? `${width}px` : width,
            height: typeof height === 'number' ? `${height}px` : height,
            maxWidth: '100%',
          }}
          data-ad-client={ADSENSE_CLIENT_ID}
          data-ad-slot={slot}
          data-ad-format={responsive ? 'auto' : undefined}
          data-full-width-responsive={responsive ? 'true' : undefined}
        ></ins>
      )}

      {adChecked && adUnfilled && (
        <Text size="xs" c="dimmed" ta="center">
          No ads available at the moment.
        </Text>
      )}
    </Box>
  );
}
