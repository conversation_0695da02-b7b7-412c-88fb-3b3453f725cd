-- Insert dummy assets for testing
-- This script creates sample assets for each asset type with realistic data

-- First, ensure we have some test contacts (these should exist from normal usage)
-- If not, you may need to create them first through the application

-- Insert dummy Badge assets
INSERT INTO assets (
  id,
  title,
  description,
  asset_type,
  image_url,
  template_id,
  issuer_odude_name,
  issuer_email,
  expiry_date,
  metadata,
  created_at,
  updated_at,
  is_deleted
) VALUES
-- Badge 1: Programming Achievement
(
  gen_random_uuid(),
  'JavaScript Master',
  'Completed advanced JavaScript programming course with distinction',
  'Badge',
  'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=300&fit=crop',
  'badge_default',
  'academy.edu',
  '<EMAIL>',
  '2025-12-31T23:59:59Z',
  '{"level": "Advanced", "category": "Programming", "skills": ["JavaScript", "ES6+", "Async/Await"]}'::jsonb,
  NOW() - INTERVAL '30 days',
  NOW() - INTERVAL '30 days',
  false
),
-- Badge 2: Design Achievement
(
  gen_random_uuid(),
  'UI/UX Design Expert',
  'Demonstrated exceptional skills in user interface and experience design',
  'Badge',
  'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=400&h=300&fit=crop',
  'badge_default',
  'design.studio',
  '<EMAIL>',
  '2026-06-30T23:59:59Z',
  '{"level": "Expert", "category": "Design", "tools": ["Figma", "Adobe XD", "Sketch"]}'::jsonb,
  NOW() - INTERVAL '15 days',
  NOW() - INTERVAL '15 days',
  false
);

-- Insert dummy Certificate assets
INSERT INTO assets (
  id,
  title,
  description,
  asset_type,
  image_url,
  template_id,
  issuer_odude_name,
  issuer_email,
  expiry_date,
  metadata,
  created_at,
  updated_at,
  is_deleted
) VALUES
-- Certificate 1: Course Completion
(
  gen_random_uuid(),
  'Full Stack Web Development Certificate',
  'Successfully completed comprehensive full stack web development program',
  'Certificate',
  'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=300&fit=crop',
  'certificate_default',
  'techuniversity.edu',
  '<EMAIL>',
  NULL, -- No expiry
  '{"institution": "Tech University", "course": "Full Stack Development", "grade": "A+", "credits": 40}'::jsonb,
  NOW() - INTERVAL '45 days',
  NOW() - INTERVAL '45 days',
  false
),
-- Certificate 2: Professional Certification
(
  gen_random_uuid(),
  'Cloud Architecture Professional',
  'Certified cloud solutions architect with expertise in AWS and Azure',
  'Certificate',
  'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=400&h=300&fit=crop',
  'certificate_default',
  'cloudcert.org',
  '<EMAIL>',
  '2027-03-15T23:59:59Z',
  '{"institution": "Cloud Certification Board", "course": "Cloud Architecture", "grade": "Professional", "provider": "AWS/Azure"}'::jsonb,
  NOW() - INTERVAL '20 days',
  NOW() - INTERVAL '20 days',
  false
);

-- Insert dummy Ticket assets
INSERT INTO assets (
  id,
  title,
  description,
  asset_type,
  image_url,
  template_id,
  issuer_odude_name,
  issuer_email,
  expiry_date,
  metadata,
  created_at,
  updated_at,
  is_deleted
) VALUES
-- Ticket 1: Conference Ticket
(
  gen_random_uuid(),
  'Tech Conference 2024 - VIP Pass',
  'VIP access to the premier technology conference with networking sessions',
  'Ticket',
  'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=300&fit=crop',
  'ticket_default',
  'techconf.events',
  '<EMAIL>',
  '2024-12-15T18:00:00Z',
  '{"event": "Tech Conference 2024", "venue": "Convention Center", "seat": "VIP-001", "access": "All Areas"}'::jsonb,
  NOW() - INTERVAL '10 days',
  NOW() - INTERVAL '10 days',
  false
),
-- Ticket 2: Workshop Access
(
  gen_random_uuid(),
  'AI Workshop Series - Premium Access',
  'Access to exclusive AI and machine learning workshop series',
  'Ticket',
  'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop',
  'ticket_default',
  'aiworkshop.tech',
  '<EMAIL>',
  '2025-01-31T23:59:59Z',
  '{"event": "AI Workshop Series", "venue": "Online + In-Person", "seat": "Premium", "sessions": 12}'::jsonb,
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '5 days',
  false
);

-- Insert dummy Coupon assets
INSERT INTO assets (
  id,
  title,
  description,
  asset_type,
  image_url,
  template_id,
  issuer_odude_name,
  issuer_email,
  expiry_date,
  metadata,
  created_at,
  updated_at,
  is_deleted
) VALUES
-- Coupon 1: Software Discount
(
  gen_random_uuid(),
  '50% Off Premium Software License',
  'Exclusive discount on premium development tools and software licenses',
  'Coupon',
  'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400&h=300&fit=crop',
  'coupon_default',
  'devtools.store',
  '<EMAIL>',
  '2024-12-31T23:59:59Z',
  '{"discount": "50%", "validUntil": "2024-12-31", "terms": "Valid for new purchases only", "minAmount": 100}'::jsonb,
  NOW() - INTERVAL '7 days',
  NOW() - INTERVAL '7 days',
  false
),
-- Coupon 2: Service Discount
(
  gen_random_uuid(),
  '25% Off Consulting Services',
  'Professional consulting services discount for new clients',
  'Coupon',
  'https://images.unsplash.com/photo-**********-6726b3ff858f?w=400&h=300&fit=crop',
  'coupon_default',
  'consulting.pro',
  '<EMAIL>',
  '2025-03-31T23:59:59Z',
  '{"discount": "25%", "validUntil": "2025-03-31", "terms": "New clients only, minimum 10 hours", "service": "Technical Consulting"}'::jsonb,
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '3 days',
  false
);

-- Insert some sample asset transfers (optional - for testing the transfer system)
-- Note: These will only work if the corresponding ODude names exist in your contact table
-- You may need to adjust the ODude names to match existing contacts in your system

-- Example transfers (uncomment and adjust ODude names as needed):
/*
INSERT INTO asset_transfers (
  id,
  asset_id,
  from_odude_name,
  to_odude_name,
  status,
  transferred_at,
  responded_at,
  response_note
) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM assets WHERE title = 'JavaScript Master' LIMIT 1),
  'academy.edu',
  'student.me', -- Adjust this to an existing ODude name
  'pending',
  NOW() - INTERVAL '2 days',
  NULL,
  NULL
),
(
  gen_random_uuid(),
  (SELECT id FROM assets WHERE title = 'UI/UX Design Expert' LIMIT 1),
  'design.studio',
  'designer.me', -- Adjust this to an existing ODude name
  'approved',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '3 days',
  'Thank you for this recognition!'
);
*/

-- Verify the inserted data
SELECT 
  asset_type,
  COUNT(*) as count,
  STRING_AGG(title, ', ') as titles
FROM assets 
WHERE is_deleted = false
GROUP BY asset_type
ORDER BY asset_type;
