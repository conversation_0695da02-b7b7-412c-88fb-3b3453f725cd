import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseAdminClient } from 'src/lib/supabaseAdmin';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication (allows both super admin and owners)
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { name } = await request.json();

    if (!name) {
      return NextResponse.json({ error: 'Contact name is required' }, { status: 400 });
    }

    // Check ownership permissions for non-super admins
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      const { isContactInOwnerNamespace } = await import('src/lib/adminAuth');
      if (!isContactInOwnerNamespace(name, authResult.ownedPrimaryNames)) {
        return NextResponse.json({ error: 'Unauthorized - contact not in your namespace' }, { status: 403 });
      }
    }

    const supabase = getSupabaseAdminClient();

    // Get current disabled status
    const { data: contact, error: fetchError } = await supabase
      .from('contact')
      .select('disabled')
      .eq('name', name)
      .single();

    if (fetchError) {
      console.error('Error fetching contact:', fetchError);
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Toggle the disabled status
    const newDisabledStatus = !contact.disabled;

    const { error: updateError } = await supabase
      .from('contact')
      .update({ disabled: newDisabledStatus })
      .eq('name', name);

    if (updateError) {
      console.error('Error updating contact:', updateError);
      return NextResponse.json({ error: 'Failed to update contact' }, { status: 500 });
    }

    return NextResponse.json({
      message: `Contact ${newDisabledStatus ? 'disabled' : 'enabled'} successfully`,
      disabled: newDisabledStatus
    });

  } catch (error) {
    console.error('Toggle contact disable error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
