import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './database.types';

// Server-only Supabase client using the service role key
// This must NEVER be imported in client components
let supabaseAdminInstance: SupabaseClient<Database> | null = null;

export function getSupabaseAdminClient(): SupabaseClient<Database> {
  // Prevent usage on the client
  if (typeof window !== 'undefined') {
    throw new Error('getSupabaseAdminClient can only be used on the server');
  }

  if (supabaseAdminInstance) return supabaseAdminInstance;

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error('Missing Supabase environment variables for admin client');
  }

  supabaseAdminInstance = createClient<Database>(supabaseUrl, serviceRoleKey);
  return supabaseAdminInstance;
}

export const supabaseAdmin = getSupabaseAdminClient();

